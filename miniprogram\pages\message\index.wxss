.message-container {
  padding: 40rpx;
  background: #f6f6f6;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.contact-info {
  background: #fff;
  border-radius: 12rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.label {
  font-size: 28rpx;
  color: #666;
  min-width: 120rpx;
}

.value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.copy-btn {
  background: #007aff;
  color: #fff;
  border-radius: 20rpx;
  padding: 8rpx 24rpx;
  font-size: 24rpx;
  border: none;
}

.qr-code {
  text-align: center;
  margin-top: 32rpx;
}

.qr-code image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 8rpx;
}

.qr-tip {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-top: 16rpx;
}

.tips {
  background: #fff;
  border-radius: 12rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
}

.tip-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 16rpx;
}

.tip-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  display: block;
  margin-bottom: 8rpx;
}

.back-btn {
  width: 100%;
  background: #007aff;
  color: #fff;
  border-radius: 24rpx;
  padding: 20rpx 0;
  font-size: 28rpx;
  border: none;
}
