# 云开发数据库结构与云函数接口文档

## 一、数据库集合设计

### 1. coaches（教练信息）
| 字段名         | 类型     | 说明                   |
| -------------- | -------- | ---------------------- |
| _id            | String   | 教练ID（自动生成）     |
| name           | String   | 教练姓名               |
| avatar         | String   | 头像URL                |
| gender         | String   | 性别                   |
| city           | String   | 所在城市               |
| gym            | String   | 常驻岩馆               |
| boulderingLevel| String   | 抱石水平               |
| leadLevel      | String   | 先锋水平               |
| certifications | [String] | 认证资质               |
| specialty      | [String] | 教学专长               |
| style          | String   | 教学风格               |
| intro          | String   | 教练简介               |
| gallery        | [String] | 成果图片/视频URL       |
| wechat         | String   | 微信号                 |
| rating         | Number   | 平均评分               |
| reviewCount    | Number   | 评价数                 |
| createdAt      | Date     | 创建时间               |
| updatedAt      | Date     | 更新时间               |

**索引建议**：city、gym、specialty、boulderingLevel、leadLevel、rating

---

### 2. courses（课程信息）
| 字段名         | 类型     | 说明                   |
| -------------- | -------- | ---------------------- |
| _id            | String   | 课程ID                 |
| coachId        | String   | 教练ID（外键）         |
| title          | String   | 课程标题               |
| type           | String   | 课程类型（私教/小班）  |
| intro          | String   | 简介                   |
| duration       | Number   | 时长（分钟）           |
| price          | Number   | 价格                   |
| location       | String   | 上课地点               |
| needDeposit    | Boolean  | 是否需定金             |
| depositAmount  | Number   | 定金金额               |
| depositRate    | Number   | 定金比例（如30%）      |
| cancelPolicy   | String   | 取消规则说明           |
| createdAt      | Date     | 创建时间               |
| updatedAt      | Date     | 更新时间               |

**索引建议**：coachId、type、price

---

### 3. reviews（评价信息）
| 字段名         | 类型     | 说明                   |
| -------------- | -------- | ---------------------- |
| _id            | String   | 评价ID                 |
| coachId        | String   | 教练ID                 |
| studentId      | String   | 学员openid             |
| appointmentId  | String   | 预约ID                 |
| courseId       | String   | 课程ID                 |
| rating         | Number   | 评分（1-5）            |
| content        | String   | 评价内容               |
| images         | [String] | 图片URL                |
| anonymous      | Boolean  | 是否匿名               |
| reply          | String   | 教练回复               |
| createdAt      | Date     | 创建时间               |

**索引建议**：coachId、courseId、studentId

---

### 4. appointments（预约信息）
| 字段名         | 类型     | 说明                   |
| -------------- | -------- | ---------------------- |
| _id            | String   | 预约ID                 |
| coachId        | String   | 教练ID                 |
| studentId      | String   | 学员openid             |
| courseId       | String   | 课程ID                 |
| courseTitle    | String   | 课程标题               |
| timeSlot       | Object   | 预约时间段 {date, start, end} |
| status         | String   | 状态（待确认/已确认/已完成/已取消）|
| deposit        | Number   | 定金金额               |
| depositStatus  | String   | 定金状态（已支付/已退回/已结算）|
| studentName    | String   | 学员姓名               |
| studentPhone   | String   | 学员电话               |
| remark         | String   | 备注                   |
| createdAt      | Date     | 创建时间               |
| updatedAt      | Date     | 更新时间               |

**索引建议**：coachId、studentId、courseId、status

---

### 5. coach_availability（教练可预约时间）
| 字段名         | 类型     | 说明                   |
| -------------- | -------- | ---------------------- |
| _id            | String   | 主键                   |
| coachId        | String   | 教练ID                 |
| date           | String   | 日期（YYYY-MM-DD）     |
| slots          | [Object] | 可预约时段 [{start, end}]|
| createdAt      | Date     | 创建时间               |
| updatedAt      | Date     | 更新时间               |

**索引建议**：coachId、date

---

## 二、主要云函数接口文档

### 1. searchCoaches
- **功能**：多条件筛选/搜索教练列表
- **入参**：
  - keyword: String（关键词，支持姓名、岩馆、专长模糊搜索）
  - city: String
  - level: String
  - specialty: String
  - priceRange: [Number, Number]
  - sortBy: String（rating/distance）
  - location: {lat, lng}（用于距离排序，可选）
  - page: Number
  - pageSize: Number
- **出参**：
  - coaches: [Object]（教练列表，含主要展示字段）
  - total: Number

---

### 2. getCoachDetails
- **功能**：获取教练详情（含个人信息、课程、定金规则、成果等）
- **入参**：
  - coachId: String
- **出参**：
  - coach: Object（教练基本信息）
  - courses: [Object]（该教练所有课程）

---

### 3. getCoachReviews
- **功能**：获取教练评价列表
- **入参**：
  - coachId: String
  - page: Number
  - pageSize: Number
- **出参**：
  - reviews: [Object]
  - total: Number

---

### 4. getAvailableSlots
- **功能**：获取教练某课程可预约时间段
- **入参**：
  - coachId: String
  - courseId: String
  - date: String（可选，指定日期）
- **出参**：
  - slots: [Object]（[{date, start, end, isBooked}]）

---

### 5. createAppointment
- **功能**：创建预约
- **入参**：
  - coachId: String
  - studentId: String
  - courseId: String
  - timeSlot: {date, start, end}
  - studentName: String
  - studentPhone: String
  - remark: String
- **出参**：
  - success: Boolean
  - appointmentId: String
  - needDeposit: Boolean
  - depositAmount: Number

---

### 6. getMyAppointments
- **功能**：获取当前学员的所有预约
- **入参**：
  - studentId: String（openid）
  - status: String（可选，进行中/已完成/已取消）
- **出参**：
  - appointments: [Object]

---

### 7. submitReview
- **功能**：提交评价
- **入参**：
  - coachId: String
  - studentId: String
  - appointmentId: String
  - courseId: String
  - rating: Number
  - content: String
  - images: [String]
  - anonymous: Boolean
- **出参**：
  - success: Boolean

---

### 8. getCoachSchedule
- **功能**：教练端获取自己的日历和预约情况
- **入参**：
  - coachId: String
  - month: String（YYYY-MM）
- **出参**：
  - schedule: [Object]（含每日可约/已约满/空闲状态）

---

### 9. 其他接口（可按需扩展）
- confirmAppointment
- cancelAppointmentByStudent
- cancelAppointmentByCoach
- replyToReview
- createCourse / updateCourse / deleteCourse

---

> 本文档可直接用于云开发数据库集合创建和云函数接口实现参考。 