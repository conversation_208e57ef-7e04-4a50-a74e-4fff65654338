const cloud = require('wx-server-sdk')
cloud.init()
const db = cloud.database()

exports.main = async (event, context) => {
  const { appointmentId, status } = event
  
  // 获取用户openid
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  if (!appointmentId || !status) {
    return {
      success: false,
      error: '参数不完整'
    }
  }
  
  try {
    // 获取教练信息
    const coachRes = await db.collection('coaches')
      .where({ openid: openid })
      .get()
    
    if (coachRes.data.length === 0) {
      return {
        success: false,
        error: '教练信息不存在'
      }
    }
    
    const coach = coachRes.data[0]
    const coachId = coach._id
    
    // 获取预约信息并验证权限
    const appointmentRes = await db.collection('appointments').doc(appointmentId).get()
    if (!appointmentRes.data) {
      return {
        success: false,
        error: '预约不存在'
      }
    }
    
    const appointment = appointmentRes.data
    if (appointment.coachId !== coachId) {
      return {
        success: false,
        error: '无权限操作此预约'
      }
    }
    
    // 更新预约状态
    const updateData = {
      status: status,
      updatedAt: new Date()
    }
    
    // 如果是确认预约，添加确认时间
    if (status === '已确认') {
      updateData.confirmedAt = new Date()
    }
    
    // 如果是完成课程，添加完成时间
    if (status === '已完成') {
      updateData.completedAt = new Date()
    }
    
    await db.collection('appointments').doc(appointmentId).update({
      data: updateData
    })
    
    return {
      success: true,
      message: '预约状态更新成功'
    }
  } catch (error) {
    console.error('更新预约状态失败：', error)
    return {
      success: false,
      error: error.message
    }
  }
}
