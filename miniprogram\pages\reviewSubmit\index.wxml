<view class="review-container">
  <view class="header">
    <text class="title">评价教练</text>
  </view>

  <view class="course-info" wx:if="{{appointment}}">
    <view class="coach-avatar">
      <image src="{{coach.avatar}}" mode="aspectFill" />
    </view>
    <view class="course-details">
      <text class="coach-name">{{coach.name}}</text>
      <text class="course-title">{{appointment.courseTitle}}</text>
      <text class="course-time">{{appointment.timeSlot.date}} {{appointment.timeSlot.start}}-{{appointment.timeSlot.end}}</text>
    </view>
  </view>

  <view class="rating-section">
    <text class="section-title">课程评分</text>
    <view class="stars">
      <block wx:for="{{5}}" wx:key="*this">
        <text
          class="star {{rating >= (index + 1) ? 'active' : ''}}"
          bindtap="setRating"
          data-rating="{{index + 1}}"
        >
          ★
        </text>
      </block>
      <text class="rating-text">{{ratingTexts[rating] || '请评分'}}</text>
    </view>
  </view>

  <!-- 标签评价区域 -->
  <view class="tags-section">
    <text class="section-title">选择标签评价 (最多8个)</text>
    <text class="section-desc">选择符合您体验的标签，帮助其他学员了解教练特点</text>

    <!-- 正面标签 -->
    <block wx:for="{{reviewTags}}" wx:key="*this" wx:for-item="category" wx:for-index="categoryKey">
      <view class="tag-category">
        <view class="category-header">
          <text class="category-title" style="color: {{category.color}}">{{category.title}}</text>
        </view>
        <view class="tag-list">
          <block wx:for="{{category.tags}}" wx:key="id" wx:for-item="tag">
            <view
              class="tag-item {{selectedTags.indexOf(tag.id) !== -1 ? 'selected' : ''}}"
              style="border-color: {{selectedTags.indexOf(tag.id) !== -1 ? category.color : '#e5e5e5'}}; background: {{selectedTags.indexOf(tag.id) !== -1 ? category.color + '20' : '#fff'}}"
              bindtap="toggleTag"
              data-tag-id="{{tag.id}}"
            >
              <text class="tag-icon">{{tag.icon}}</text>
              <text class="tag-text" style="color: {{selectedTags.indexOf(tag.id) !== -1 ? category.color : '#333'}}">{{tag.text}}</text>
            </view>
          </block>
        </view>
      </view>
    </block>

    <!-- 改进建议标签 -->
    <view class="negative-tags-section">
      <view class="negative-toggle" bindtap="toggleNegativeTags">
        <text class="toggle-text">{{showNegativeTags ? '收起' : '展开'}}改进建议</text>
        <text class="toggle-icon">{{showNegativeTags ? '▲' : '▼'}}</text>
      </view>

      <view class="tag-category" wx:if="{{showNegativeTags}}">
        <view class="tag-list">
          <block wx:for="{{negativeTags.improvement.tags}}" wx:key="id" wx:for-item="tag">
            <view
              class="tag-item negative {{selectedTags.indexOf(tag.id) !== -1 ? 'selected' : ''}}"
              bindtap="toggleTag"
              data-tag-id="{{tag.id}}"
            >
              <text class="tag-icon">{{tag.icon}}</text>
              <text class="tag-text">{{tag.text}}</text>
            </view>
          </block>
        </view>
      </view>
    </view>
  </view>

  <!-- 后台留言功能 -->
  <view class="feedback-section">
    <text class="section-title">意见反馈 (可选)</text>
    <text class="section-desc">如果您有其他建议或希望添加新的评价标签，请告诉我们</text>
    <textarea
      class="feedback-input"
      placeholder="您的建议将帮助我们完善评价系统..."
      bindinput="onFeedbackInput"
      value="{{feedback}}"
      maxlength="200"
    />
    <text class="char-count">{{feedback.length || 0}}/200</text>
  </view>

  <view class="anonymous-section">
    <label class="anonymous-checkbox">
      <checkbox checked="{{anonymous}}" bindchange="onAnonymousChange" />
      <text>匿名评价</text>
    </label>
  </view>

  <view class="submit-section">
    <button
      class="submit-btn"
      bindtap="submitReview"
      disabled="{{!canSubmit}}"
    >
      提交评价
    </button>
  </view>
</view>