const cloud = require("wx-server-sdk");
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV,
});

const db = cloud.database();
// 获取openid
const getOpenId = async () => {
  // 获取基础信息
  const wxContext = cloud.getWXContext();
  return {
    openid: wxContext.OPENID,
    appid: wxContext.APPID,
    unionid: wxContext.UNIONID,
  };
};

// 获取小程序二维码
const getMiniProgramCode = async () => {
  // 获取小程序二维码的buffer
  const resp = await cloud.openapi.wxacode.get({
    path: "pages/index/index",
  });
  const { buffer } = resp;
  // 将图片上传云存储空间
  const upload = await cloud.uploadFile({
    cloudPath: "code.png",
    fileContent: buffer,
  });
  return upload.fileID;
};

// 创建集合
const createCollection = async () => {
  try {
    // 创建集合
    await db.createCollection("sales");
    await db.collection("sales").add({
      // data 字段表示需新增的 JSON 数据
      data: {
        region: "华东",
        city: "上海",
        sales: 11,
      },
    });
    await db.collection("sales").add({
      // data 字段表示需新增的 JSON 数据
      data: {
        region: "华东",
        city: "南京",
        sales: 11,
      },
    });
    await db.collection("sales").add({
      // data 字段表示需新增的 JSON 数据
      data: {
        region: "华南",
        city: "广州",
        sales: 22,
      },
    });
    await db.collection("sales").add({
      // data 字段表示需新增的 JSON 数据
      data: {
        region: "华南",
        city: "深圳",
        sales: 22,
      },
    });
    return {
      success: true,
    };
  } catch (e) {
    // 这里catch到的是该collection已经存在，从业务逻辑上来说是运行成功的，所以catch返回success给前端，避免工具在前端抛出异常
    return {
      success: true,
      data: "create collection success",
    };
  }
};

// 查询数据
const selectRecord = async () => {
  // 返回数据库查询结果
  return await db.collection("sales").get();
};

// 更新数据
const updateRecord = async (event) => {
  try {
    // 遍历修改数据库信息
    for (let i = 0; i < event.data.length; i++) {
      await db
        .collection("sales")
        .where({
          _id: event.data[i]._id,
        })
        .update({
          data: {
            sales: event.data[i].sales,
          },
        });
    }
    return {
      success: true,
      data: event.data,
    };
  } catch (e) {
    return {
      success: false,
      errMsg: e,
    };
  }
};

// 新增数据
const insertRecord = async (event) => {
  try {
    const insertRecord = event.data;
    // 插入数据
    await db.collection("sales").add({
      data: {
        region: insertRecord.region,
        city: insertRecord.city,
        sales: Number(insertRecord.sales),
      },
    });
    return {
      success: true,
      data: event.data,
    };
  } catch (e) {
    return {
      success: false,
      errMsg: e,
    };
  }
};

// 删除数据
const deleteRecord = async (event) => {
  try {
    await db
      .collection("sales")
      .where({
        _id: event.data._id,
      })
      .remove();
    return {
      success: true,
    };
  } catch (e) {
    return {
      success: false,
      errMsg: e,
    };
  }
};

// 初始化教练数据
const initCoachesData = async () => {
  const coachesCollection = db.collection('coaches');

  // 先清除旧数据，然后添加新数据
  try {
    await coachesCollection.where({}).remove();
    console.log('已清除旧的教练数据');
  } catch (error) {
    console.log('清除旧数据时出错（可能没有旧数据）：', error);
  }

  // 示例教练数据
  const coachesData = [
    {
      name: '张岩教练',
      phone: '13800138001',
      avatar: 'https://via.placeholder.com/100x100/4CAF50/FFFFFF?text=张岩',
      gender: '男',
      city: '北京',
      gym: '奥莱攀岩馆',
      boulderingLevel: '抱石V6',
      leadLevel: '先锋5.12a',
      certifications: ['CWA L1'],
      specialty: ['入门教学', '心理辅导'],
      style: '耐心细致，擅长帮助克服恐高',
      intro: '5年攀岩经验，专注于新手教学，已帮助200+学员入门攀岩',
      gallery: [],
      wechat: 'zhangyan_climb',
      rating: 4.8,
      reviewCount: 12,
      courseName: '私教课',
      price: 680,
      needDeposit: true,
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      name: '李峰教练',
      phone: '13800138002',
      avatar: 'https://via.placeholder.com/100x100/2196F3/FFFFFF?text=李峰',
      gender: '男',
      city: '北京',
      gym: '攀岩工厂',
      boulderingLevel: '抱石V8',
      leadLevel: '先锋5.13a',
      certifications: ['CWA L2', 'IFSC'],
      specialty: ['动态技巧', '耐力训练'],
      style: '技术流，注重动作细节和力量训练',
      intro: '8年攀岩经验，曾获得全国攀岩比赛前三名',
      gallery: [],
      wechat: 'lifeng_climbing',
      rating: 4.5,
      reviewCount: 8,
      courseName: '私教课',
      price: 800,
      needDeposit: true,
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      name: '王雪教练',
      phone: '13800138003',
      avatar: 'https://via.placeholder.com/100x100/E91E63/FFFFFF?text=王雪',
      gender: '女',
      city: '上海',
      gym: '岩时攀岩馆',
      boulderingLevel: '抱石V5',
      leadLevel: '先锋5.11d',
      certifications: ['CWA L1'],
      specialty: ['女性攀岩', '柔韧性训练'],
      style: '温和耐心，特别擅长女性学员教学',
      intro: '专业攀岩教练，女性攀岩推广者，已培训女性学员100+',
      gallery: [],
      wechat: 'wangxue_climb',
      rating: 4.9,
      reviewCount: 15,
      courseName: '私教课',
      price: 650,
      needDeposit: true,
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      name: '陈强教练',
      phone: '13800138004',
      avatar: 'https://via.placeholder.com/100x100/FF9800/FFFFFF?text=陈强',
      gender: '男',
      city: '深圳',
      gym: '岩壁芭蕾',
      boulderingLevel: '抱石V7',
      leadLevel: '先锋5.12c',
      certifications: ['CWA L2'],
      specialty: ['力量训练', '竞技攀岩'],
      style: '严格专业，注重基础功底和竞技技巧',
      intro: '前国家队队员，10年攀岩经验，专业竞技攀岩教练',
      gallery: [],
      wechat: 'chenqiang_pro',
      rating: 4.7,
      reviewCount: 20,
      courseName: '私教课',
      price: 900,
      needDeposit: true,
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      name: '刘明教练',
      phone: '13800138005',
      avatar: 'https://via.placeholder.com/100x100/9C27B0/FFFFFF?text=刘明',
      gender: '男',
      city: '广州',
      gym: '攀越攀岩馆',
      boulderingLevel: '抱石V4',
      leadLevel: '先锋5.11a',
      certifications: ['CWA L1'],
      specialty: ['入门教学', '安全教育'],
      style: '安全第一，循序渐进，注重基础',
      intro: '安全攀岩倡导者，3年教学经验，零事故记录',
      gallery: [],
      wechat: 'liuming_safe',
      rating: 4.6,
      reviewCount: 10,
      courseName: '私教课',
      price: 580,
      needDeposit: false,
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];

  try {
    const tasks = coachesData.map(coach => coachesCollection.add({ data: coach }));
    await Promise.all(tasks);

    console.log('教练数据初始化成功');
    return {
      success: true,
      message: '教练数据初始化成功',
      count: coachesData.length
    };
  } catch (err) {
    console.error('教练数据初始化失败：', err);
    return {
      success: false,
      message: '教练数据初始化失败',
      error: err
    };
  }
};

// 清除教练数据
const clearCoachesData = async () => {
  try {
    const result = await db.collection('coaches').where({}).remove();
    console.log('教练数据清除成功');
    return {
      success: true,
      message: '教练数据清除成功',
      deletedCount: result.stats.removed
    };
  } catch (err) {
    console.error('教练数据清除失败：', err);
    return {
      success: false,
      message: '教练数据清除失败',
      error: err
    };
  }
};

// 查看教练数据
const listCoachesData = async () => {
  try {
    const result = await db.collection('coaches').get();
    console.log('教练数据查询成功');
    return {
      success: true,
      message: '教练数据查询成功',
      data: result.data.map(coach => ({
        _id: coach._id,
        name: coach.name,
        phone: coach.phone,
        openid: coach.openid || '未绑定'
      }))
    };
  } catch (err) {
    console.error('教练数据查询失败：', err);
    return {
      success: false,
      message: '教练数据查询失败',
      error: err
    };
  }
};

// const getOpenId = require('./getOpenId/index');
// const getMiniProgramCode = require('./getMiniProgramCode/index');
// const createCollection = require('./createCollection/index');
// const selectRecord = require('./selectRecord/index');
// const updateRecord = require('./updateRecord/index');
// const sumRecord = require('./sumRecord/index');
// const fetchGoodsList = require('./fetchGoodsList/index');
// const genMpQrcode = require('./genMpQrcode/index');
// 云函数入口函数
exports.main = async (event, context) => {
  switch (event.type) {
    case "getOpenId":
      return await getOpenId();
    case "getMiniProgramCode":
      return await getMiniProgramCode();
    case "createCollection":
      return await createCollection();
    case "selectRecord":
      return await selectRecord();
    case "updateRecord":
      return await updateRecord(event);
    case "insertRecord":
      return await insertRecord(event);
    case "deleteRecord":
      return await deleteRecord(event);
    case "initCoachesData":
      return await initCoachesData();
    case "clearCoachesData":
      return await clearCoachesData();
    case "listCoachesData":
      return await listCoachesData();
  }
};
