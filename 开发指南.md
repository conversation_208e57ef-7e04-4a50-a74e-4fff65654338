# 攀岩自由教练预约小程序开发指南（微信云开发 MVP 快速实现版）

## 1. 项目概述
### 1.1 核心定位
本项目旨在为攀岩自由教练打造一个一站式个人品牌与预约管理平台，通过连接教练与学员，有效提升信任度、减少爽约，并帮助教练更好地展现其专业价值。

### 1.2 目标用户
- **核心用户**：攀岩自由教练
- **重要用户**：攀岩学员（新手、进阶者）
- **潜在用户**：攀岩馆（合作推广）

### 1.3 解决痛点
| 角色   | 痛点 | 小程序解决方案 |
|--------|------|----------------|
| 教练   | 1. 学员爽约造成时间/收入损失 | 强制定金预约制度 + 清晰的取消/退款政策 |
|        | 2. 不擅长营销，被"会包装"但技术一般的教练抢客 | 提供专业能力展示模板 + 真实学员评价体系 + 学员成果展示（视频/图片） |
|        | 3. 缺乏便捷的预约管理工具（微信/电话沟通效率低） | 集成化预约日历 + 自动提醒（开课前） |
|        | 4. 难以积累和展示教学成果/口碑 | 结构化评价系统 + 学员成长案例库（需学员授权） |
|        | 5. 同行竞争激烈，缺乏差异化展示 | 个性化教练主页 + 能力标签化 + 细分领域专长展示 |
| 学员   | 1. 难以判断教练真实水平和教学风格是否适合自己 | 透明化的教练主页（资质、经历、专长） + 大量真实学员评价 + 横向对比功能 |
|        | 2. 找不到可靠的、符合自己需求的自由教练 | 强大的搜索/筛选功能（按水平、风格、专长、价格、地点） + 教练排名/推荐 |
|        | 3. 预约流程繁琐（反复沟通时间地点） | 在线实时查看教练可约时间 + 一键预约（含定金支付） |
|        | 4. 担心教练不靠谱或课程效果 | 定金保障（平台监管）+ 真实评价参考 + 清晰的课程大纲/目标 |

---

## 2. 功能模块设计与开发指引
本项目分为教练端和学员端两大核心模块。在微信云开发模式下，核心业务逻辑将主要通过云函数实现。

### 2.1 教练端功能
#### 2.1.1 个人主页管理
- **功能描述**：教练可在此管理和展示个人基本信息、核心能力、认证资质、教学理念和学员成果。
- **开发要点（云开发）**：
  - 数据库集合：`coaches`，存储教练所有个人信息。
  - 文件存储：云存储上传教练头像、认证证书图片、学员成果展示的视频/图片。
  - 云函数：如 `updateCoachProfile`，实现对 `coaches` 集合的增删改查。
  - 学员成果授权：学员端完成授权后，将授权信息存储在关联集合，并在云函数中校验授权状态。

#### 2.1.2 课程管理与发布
- **功能描述**：教练发布不同类型的课程，设置课程详情、价格和可预约时间。
- **开发要点（云开发）**：
  - 数据库集合：`courses`，存储课程标题、类型、大纲、目标、时长、价格等。
  - 云函数：`createCourse`、`updateCourse`、`deleteCourse`。
  - 时间块绑定：课程与教练可预约时间块逻辑关联，价格和定金直接存储在 `courses` 集合。

#### 2.1.3 预约管理（核心模块）
- **功能描述**：教练通过可视化日历管理可预约时间、查看新预约、确认定金和管理学员。
- **开发要点（云开发）**：
  - 数据库集合：
    - `coach_availability`：存储教练可预约时间段。
    - `appointments`：存储学员预约记录。
  - 云函数：`addAvailabilitySlot`、`removeAvailabilitySlot`、`getCoachSchedule`、`confirmAppointment`、`cancelAppointmentByCoach`。
  - 日历联动：前端日历组件通过 `getCoachSchedule` 获取数据，渲染不同状态。
  - 实时通知：利用云调用或定时触发器结合微信订阅消息推送新预约和开课前提醒。

#### 2.1.4 评价管理
- **功能描述**：教练查看学员评价并进行回复。
- **开发要点（云开发）**：
  - 数据库集合：`reviews`，存储学员评价。
  - 云函数：`getCoachReviews`、`replyToReview`。

#### 2.1.5 收入与提现
- **功能描述**：教练查看收入明细并申请提现。
- **开发要点（云开发）**：
  - 数据库集合：`transactions`（支付流水和定金结算）、`withdrawals`（提现申请）。
  - 云函数：`getCoachEarnings`、`applyForWithdrawal`。
  - 微信支付商户平台对接（MVP 阶段建议）：提现可先线下人工打款，后续可对接企业付款接口。
  - 定金管理：云函数处理定金冻结和结算逻辑。

#### 2.1.6 营销工具（增值点）
- **功能描述**：提供优惠券、推荐计划和内容分享功能。
- **开发要点（云开发）**：
  - 数据库集合：`coupons`、`referrals`。
  - 云函数：`createCoupon`、`redeemCoupon`、`generateShareLink`。
  - 分享：利用微信小程序 `onShareAppMessage` API 实现。

---

### 2.2 学员端功能
#### 2.2.1 发现与搜索教练（核心入口）
- **功能描述**：学员通过多维度筛选和搜索找到合适的教练。
- **开发要点（云开发）**：
  - 云数据库查询：结合索引实现高效模糊搜索、多维筛选和排序。
  - 云函数：`searchCoaches`，封装复杂查询逻辑。
  - 地理位置查询：结合微信地理位置API，云函数计算附近教练。
  - 推荐：`recommendCoaches` 云函数根据学员信息推荐。

#### 2.2.2 教练主页（信任转化关键）
- **功能描述**：学员详细了解教练信息，特别是真实评价。
- **开发要点（云开发）**：
  - 云函数：`getCoachDetails`，一次性获取教练所有信息。
  - 动态渲染：前端根据返回数据渲染。
  - 定金规则：从 `courses` 集合读取定金设置和取消政策，前端展示。
  - 评价显示：通过 `getCoachReviews` 获取评价，多维度展示。

#### 2.2.3 预约与支付
- **功能描述**：学员选择可约时间并提交预约申请，支付定金。
- **开发要点（云开发）**：
  - 云函数：
    - `getAvailableSlots`：查询可用时段。
    - `createAppointment`：原子操作检查时段可用性，创建预约并引导支付。
  - 用户输入：收集学员姓名、电话等信息，存储到 `appointments`。
  - 平台免责提示：前端强制显示免责声明。

#### 2.2.4 我的预约
- **功能描述**：学员查看所有预约记录。
- **开发要点（云开发）**：
  - 云函数：`getMyAppointments`，根据 OpenID 查询预约。
  - 状态管理：分类展示不同状态。
  - 取消预约：`cancelAppointmentByStudent` 云函数，内含定金规则和退款逻辑。

#### 2.2.5 评价与反馈
- **功能描述**：学员在课程结束后对教练评价。
- **开发要点（云开发）**：
  - 云函数：`submitReview`，接收评价数据。
  - 权限校验：校验身份、课程完成状态、冷却期、防止重复评价。

#### 2.2.6 消息中心
- **功能描述**：学员与教练沟通（MVP 阶段建议引导至微信）。
- **开发要点（云开发）**：
  - MVP 阶段：提供教练微信联系方式复制按钮。
  - 后续迭代：可基于云开发消息队列或即时通信服务实现站内信。

---

### 2.3 平台核心机制
#### 2.3.1 定金规则引擎
- **功能描述**：强制执行定金制度和取消/退款政策。
- **开发要点（云开发）**：
  - 云函数：在 `createCourse` 存储定金和取消政策，在 `createAppointment`、`cancelAppointmentByStudent`、`cancelAppointmentByCoach` 等严格执行。
  - 事务操作：涉及资金和状态变更的操作用数据库事务，确保一致性。
  - 定时任务：定期检查未结算订单或处理过期定金。

#### 2.3.2 评价体系与防刷机制
- **功能描述**：确保评价真实性与公正性。
- **开发要点（云开发）**：
  - 权限校验：如 `submitReview` 云函数严格校验。
  - 内容过滤：集成腾讯云内容安全API或自建关键词过滤。
  - 人工审核入口：后台管理界面提供人工审核。

#### 2.3.3 信任与安全
- **功能描述**：保障平台用户和交易安全。
- **开发要点（云开发）**：
  - 教练身份审核：注册时上传身份证、资质证书，后台人工审核。
  - 用户认证：微信授权获取 openid，云函数用 openid 作为唯一标识。
  - 支付安全：全程微信支付官方接口。
  - 数据隐私：敏感数据加密存储，严格控制读写权限。
  - 平台客服：利用微信客服功能提供支持。

#### 2.3.4 数据分析（后台）
- **功能描述**：为教练和平台提供运营数据支持。
- **开发要点（云开发）**：
  - 日志服务：记录用户行为、API调用、支付事件等。
  - 云函数聚合查询：统计预约量、收入趋势、热门课程等。
  - 云开发控制台：利用自带数据监控和分析。
  - 自定义后台管理：可基于云开发构建后台管理系统。

---

## 3. 技术栈与架构建议（微信云开发）
### 3.1 技术栈
- **前端（微信小程序）**：
  - 框架：微信小程序原生开发框架
  - 语言：JavaScript (ES6+)
  - 样式：WXSS
  - 组件库：可选 Vant Weapp、WeUI
- **后端（微信云开发）**：
  - 云函数：Node.js，运行于腾讯云环境，处理业务逻辑、数据库、文件存储、微信API
  - 云数据库（NoSQL）：MongoDB，JSON文档存储，支持聚合、事务
  - 云存储：存储图片、视频、文件等静态资源
  - 云托管（可选）：如需复杂服务可用
  - 云调用：调用微信开放平台API（如支付、订阅消息）

### 3.2 架构设计（微信云开发）
- **一体化开发**：前端小程序代码直接与云函数、云数据库、云存储集成，省去传统前后端分离部署和维护成本。
- **Serverless 架构**：业务逻辑在云函数中按需运行，无需管理服务器，自动扩缩容，降低运维压力。
- **API 设计**：云函数即API接口，通过小程序端 `wx.cloud.callFunction` 调用。
- **安全性考虑**：
  - 权限控制：灵活数据库读写权限配置，敏感操作仅云函数可访问。
  - API 鉴权：云函数自动获取 openid/unionid，便于身份验证。
  - 数据加密：内部数据传输安全，敏感数据可额外加密存储。
- **性能优化**：
  - 数据库索引：合理创建索引，优化查询性能。
  - 云函数冷启动优化：优化代码，减少启动时间。
  - 文件存储CDN：云存储默认支持CDN加速，提升图片/视频加载速度。

## 学员端首页（发现教练）开发指引

### 功能描述
学员通过多维度筛选和搜索找到合适的教练。

### 开发要点（微信云开发）
- 云数据库查询：结合索引实现高效模糊搜索、多维筛选和排序。
- 云函数：`searchCoaches`，封装复杂查询逻辑。
- 地理位置查询：结合微信地理位置API，云函数计算附近教练。
- 推荐：`recommendCoaches` 云函数根据学员信息推荐。

### 相关数据结构
- coaches集合，用户地理位置，推荐算法结果。

### 相关云函数
- searchCoaches
- recommendCoaches

### 跳转逻辑
- 点击教练卡片进入教练详情页。 

# 页面开发详情

## 1. 页面结构与导航

### 1.1 主要页面列表
#### 学员端
1. 首页（发现教练）
2. 教练详情页
3. 课程预约页
4. 支付成功页
5. 我的预约
6. 预约详情页
7. 评价提交页
8. 消息中心/教练联系方式

#### 教练端
1. 教练后台首页（数据总览）
2. 个人主页管理
3. 课程管理
4. 时间管理日历
5. 预约管理
6. 预约详情页
7. 评价管理
8. 收入与提现
9. 营销工具

### 1.2 页面跳转逻辑
```mermaid
graph TD
  %% 学员端
  A[学员首页-发现教练] --> B[教练详情页]
  B --> C[课程预约页]
  C --> D[支付成功页]
  D --> E[我的预约]
  E --> F[预约详情页]
  F --> G[评价提交页]
  E --> G
  A --> E
  E --> C
  B --> E
  C --> H[消息中心/教练联系方式]
  B --> H
  F --> C

  %% 教练端
  I[教练后台首页] --> J[个人主页管理]
  I --> K[课程管理]
  I --> L[时间管理日历]
  I --> M[预约管理]
  I --> N[评价管理]
  I --> O[收入与提现]
  I --> P[营销工具]
  M --> Q[预约详情页]
  Q --> M
  L --> M
  M --> L
```

**页面跳转说明：**
- 学员端：
  - 首页可进入教练详情页、我的预约。
  - 教练详情页可进入课程预约页、消息中心、我的预约。
  - 课程预约页提交后进入支付成功页，支付成功后进入我的预约。
  - 我的预约可进入预约详情页、评价提交页、课程预约页（再次预约）。
  - 预约详情页可进入评价提交页、课程预约页（重新预约）。
  - 课程预约页、教练详情页均可进入消息中心。
- 教练端：
  - 后台首页可进入各功能模块。
  - 预约管理、时间管理日历可互相跳转。
  - 预约管理可进入预约详情页，详情页返回预约管理。

**补充说明：**
- 所有详情页均可返回上一页。
- 评价提交页、消息中心等操作完成后返回来源页。
- 支付成功页为预约流程的中转页，便于后续扩展支付逻辑。
- 页面结构和跳转已覆盖所有核心业务流程，便于后续开发和维护。

---

## 2. 主要页面开发详情

### 2.1 学员端页面
#### 2.1.1 首页（发现教练）
- **功能**：展示教练列表，支持多维度筛选/搜索、推荐、地理位置排序。
- **数据**：coaches集合，用户地理位置，推荐算法结果。
- **云函数**：searchCoaches、recommendCoaches。
- **跳转**：点击教练卡片进入教练详情页。

**原型图1：学员首页 - 发现教练（核心入口）**
(解决：快速筛选合适教练 + 突出关键信息)
```plaintext
+-------------------------------------------------------------------+
| 找攀岩教练                                                        |
| [搜索框] "输入关键词/岩馆名称"                                    |
+-------------------------------------------------------------------+
| **筛选条件**                                                      |
| [城市：北京 ▼]  [水平：入门 ▼]  [专长：动态技巧 ▼]  [价格区间 ▼]   |
+-------------------------------------------------------------------+
| **教练列表** (按评分/距离排序)                                    |
|                                                                   |
| [头像] **张岩教练** 4.8★ (12条评价)                              |
| 抱石V6 | 先锋5.12a | 常驻：奥莱攀岩馆                             |
| 专长：入门教学、心理辅导                                          |
| 私教课 ¥680/节 | [需定金]                                         |
|                                                                   |
| [头像] **李峰教练** 4.5★ (8条评价)                               |
| 抱石V8 | 先锋5.13a | 常驻：攀岩工厂                               |
| 专长：动态技巧、耐力训练                                          |
| 私教课 ¥800/节 | [需定金]                                         |
+-------------------------------------------------------------------+
| **地图模式** [切换按钮] ← 点击显示教练常驻岩馆位置                 |
+-------------------------------------------------------------------+
```
**设计要点：**  
- **首屏即筛选**：学员最关心的条件（城市、水平、专长、价格）直接暴露，减少操作步骤。  
- **列表信息优先级**：评分→能力标签→地点→价格→定金提示，符合决策逻辑。  
- **"需定金"标识**：提前告知支付规则，避免后续纠纷。  

#### 2.1.2 教练详情页
- **功能**：展示教练个人信息、课程、评价、成果展示。
- **数据**：coaches、courses、reviews集合。
- **云函数**：getCoachDetails、getCoachReviews。
- **跳转**：点击预约按钮进入课程预约页。

**原型图2：教练详情页（信任转化关键）**
(解决：全面了解教练 + 透明预约规则)
```plaintext
+-------------------------------------------------------------------+
| [返回] [分享]                                                     |
| [教练封面图]                                                     |
| [头像] **张岩教练** 4.8★ (12条评价)                              |
+-------------------------------------------------------------------+
| **能力标签**                                                      |
| [抱石: V6]  [先锋: 5.12a]  [认证: CWA L1]                        |
| 教学专长：入门教学、心理辅导                                      |
| 常驻地点：奥莱攀岩馆                                              |
| 教学风格：耐心细致，擅长帮助克服恐高                              |
+-------------------------------------------------------------------+
| **⚠️ 定金规则**                                                   |
| [图标] 需付定金：¥200 (课程费30%)                                 |
| [图标] 取消政策：提前24小时可退定金，否则不退                      |
| [图标] 支付方式：添加教练微信转账                                 |
+-------------------------------------------------------------------+
| **学员评价**                                                      |
| [5★] 李**： "教练帮我2周突破V3，动作分析超准！"                   |
| [4★] 匿名： "适合新手，但希望多演示动作"                          |
| [查看全部12条评价]                                                |
+-------------------------------------------------------------------+
| **可预约课程**                                                    |
| [私教课] 90分钟 · ¥680                                            |
| 适合零基础学员，包含基础手法、脚法练习                            |
| [查看可约时间] ← 核心按钮                                         |
+-------------------------------------------------------------------+
```
**设计要点：**  
- **结构化展示能力**：用标签快速传递关键信息（水平、认证、专长）。  
- **定金规则显眼**：独立模块+图标强化，避免学员忽略。  
- **评价真实性暗示**：显示"匿名"和具体评价内容，增强可信度。  
- **课程关联可约时间**：直接引导至预约动作，减少跳转。  

#### 2.1.3 课程预约页
- **功能**：选择课程、可预约时间段，填写信息，提交预约并支付定金。
- **数据**：courses、coach_availability、appointments集合。
- **云函数**：getAvailableSlots、createAppointment。
- **跳转**：支付成功后跳转到我的预约，或失败提示。

**原型图3：预约时间选择与提交**
(解决：直观选时间 + 明确责任边界)
```plaintext
+-------------------------------------------------------------------+
| 选择时间 - 抱石入门私教课                                         |
| 教练：张岩 | 地点：奥莱攀岩馆                                     |
+-------------------------------------------------------------------+
| **7月可用时间**                                                   |
| 周一 8日：[14:00-15:30]  [16:00-17:30]                           |
| 周二 9日：[10:00-11:30]  (已约满)                                |
| 周三 10日：[09:00-10:30]  [14:00-15:30]                          |
|                                                               |
| [选择 7月10日 14:00-15:30] ← 点击后高亮                          |
+-------------------------------------------------------------------+
| **预约信息确认**                                                  |
| 课程：抱石入门私教课 · 90分钟                                     |
| 时间：7月10日 14:00-15:30                                        |
| 价格：¥680 (需定金¥200)                                          |
|                                                               |
| 您的姓名：[输入框]                                                |
| 联系电话：[输入框]                                                |
| 备注(选填)：[输入框] "希望重点练动态发力"                         |
+-------------------------------------------------------------------+
| **重要提示**：                                                    |
| 1. 提交后，教练将联系您确认并收取定金。                           |
| 2. 平台不介入支付，请直接与教练沟通。                             |
| [提交预约申请]                                                    |
+-------------------------------------------------------------------+
```
**设计要点：**  
- **时间可视化**：直接展示可约时段和已约满状态，避免无效操作。  
- **责任分离提示**：强调"平台不介入支付"，降低法律风险。  
- **轻量信息收集**：仅需姓名+电话（避免复杂表单劝退用户）。  

#### 2.1.4 我的预约
- **功能**：展示所有预约记录，分类（进行中/已完成/已取消），支持取消预约。
- **数据**：appointments集合。
- **云函数**：getMyAppointments、cancelAppointmentByStudent。
- **跳转**：可进入评价提交页或重新预约。

**原型图4：我的预约**
(解决：预约状态一目了然+便捷操作)
```plaintext
+-------------------------------------------------------------------+
| 我的预约                                                          |
| [进行中] [已完成] [已取消]  ← Tab切换                             |
+-------------------------------------------------------------------+
| [私教课] 张岩教练 7月10日 14:00-15:30                             |
| 状态：待确认 | 定金¥200 | [取消预约]                              |
| [查看详情]                                                        |
+-------------------------------------------------------------------+
| [小班课] 李峰教练 7月12日 10:00-12:00                             |
| 状态：已完成 | 定金已结算                                         |
| [去评价]                                                          |
+-------------------------------------------------------------------+
| [私教课] 张岩教练 7月8日 16:00-17:30                              |
| 状态：已取消 | 定金已退回                                         |
| [再次预约]                                                        |
+-------------------------------------------------------------------+
```
**设计要点：**  
- **状态分类Tab**：快速切换不同状态的预约。  
- **操作直观**：进行中可取消，已完成可评价，已取消可再次预约。  
- **定金状态提示**：每条预约都显示定金状态，增强透明度。  
- **详情入口**：可查看每条预约的详细信息。

#### 2.1.5 评价提交页
- **功能**：对已完成课程进行评价，打分、文字、图片。
- **数据**：reviews集合。
- **云函数**：submitReview。
- **跳转**：提交后返回我的预约或教练详情页。

**原型图5：评价提交页**
(解决：收集真实反馈+防刷机制)
```plaintext
+-------------------------------------------------------------------+
| 评价教练 - 张岩教练                                               |
| 课程：抱石入门私教课 7月10日 14:00-15:30                          |
+-------------------------------------------------------------------+
| 评分：[★★★★★]  ← 星级打分                                         |
| 评价内容：[多行输入框]                                             |
| [添加图片] (最多3张)                                              |
| 匿名评价：[✓]                                                     |
+-------------------------------------------------------------------+
| [提交评价]                                                        |
+-------------------------------------------------------------------+
```
**设计要点：**  
- **星级评分+文字**：结构化收集分数和文字反馈。  
- **图片上传**：支持学员上传成果/现场照片。  
- **匿名选项**：保护隐私，鼓励真实反馈。  
- **防刷机制**：仅已完成课程可评价，且每个预约仅能评价一次。

#### 2.1.6 消息中心/教练联系方式
- **功能**：展示教练微信号，复制按钮，或后续迭代为站内信。
- **数据**：coaches集合。
- **云函数**：getCoachDetails。
- **跳转**：返回上一页。

**原型图6：消息中心/教练联系方式**
(解决：便捷沟通+隐私保护)
```plaintext
+-------------------------------------------------------------------+
| 联系教练                                                          |
+-------------------------------------------------------------------+
| 微信号：zhangyan_climb [复制]                                     |
| [二维码]（如有）                                                  |
| 温馨提示：请通过微信与教练沟通课程细节和定金支付。                |
+-------------------------------------------------------------------+
| [返回]                                                            |
+-------------------------------------------------------------------+
```
**设计要点：**  
- **一键复制微信号**：降低沟通门槛。  
- **二维码展示**：如有可直接扫码加好友。  
- **隐私提示**：提醒用户平台不介入支付，保护双方权益。

---

### 2.2 教练端页面
#### 2.2.1 教练后台首页（数据总览）
- **功能**：展示预约量、收入、评价等核心数据。
- **数据**：appointments、transactions、reviews集合。
- **云函数**：getCoachEarnings、getCoachReviews、getCoachSchedule。
- **跳转**：各功能模块入口。

**原型图7：教练后台首页**
(解决：核心数据一目了然+高效入口)
```plaintext
+-------------------------------------------------------------------+
| 教练后台                                                          |
+-------------------------------------------------------------------+
| 今日预约：3 | 本月收入：¥2040 | 新评价：2                          |
+-------------------------------------------------------------------+
| [个人主页管理] [课程管理] [时间管理日历] [预约管理]                |
| [评价管理] [收入与提现] [营销工具]                                |
+-------------------------------------------------------------------+
| 近期预约提醒                                                      |
| 7月12日 10:00-12:00 小班课 2人                                    |
| 7月13日 14:00-15:30 私教课                                        |
+-------------------------------------------------------------------+
```
**设计要点：**  
- **核心数据卡片**：今日预约、本月收入、新评价等一目了然。  
- **功能入口清晰**：各模块一键直达。  
- **近期预约提醒**：方便教练合理安排时间。

#### 2.2.2 个人主页管理
- **功能**：编辑个人信息、上传头像/证书、展示学员成果。
- **数据**：coaches集合，云存储。
- **云函数**：updateCoachProfile。
- **跳转**：返回后台首页。

**原型图8：个人主页管理**
(解决：专业形象打造+成果展示)
```plaintext
+-------------------------------------------------------------------+
| 编辑个人主页                                                      |
+-------------------------------------------------------------------+
| 头像：[上传]  姓名：[输入]                                        |
| 认证资质：[上传证书]                                              |
| 攀岩经历：[多行输入]                                              |
| 能力标签：[多选/自定义]                                           |
| 教学风格：[多行输入]                                              |
| 学员成果展示：[上传图片/视频]                                      |
+-------------------------------------------------------------------+
| [保存]                                                            |
+-------------------------------------------------------------------+
```
**设计要点：**  
- **多维度信息编辑**：支持头像、标签、风格、成果等。  
- **成果展示**：可上传图片/视频，增强说服力。  
- **认证上传**：便于平台审核和学员信任。

#### 2.2.3 课程管理
- **功能**：新增/编辑/删除课程，设置定金、价格、课程大纲。
- **数据**：courses集合。
- **云函数**：createCourse、updateCourse、deleteCourse。
- **跳转**：返回后台首页。

**原型图3：教练后台 - 发布课程/设置定金**
(解决：课程发布与定金规则透明化)
```plaintext
+-----------------------------------------------------+
| **发布新课程**                                      |
|                                                     |
| 课程标题：[抱石入门私教课]                          |
| 类型：私教课                                        |
| 简介：适合零基础...                                 |
| 时长：[90分钟]  价格：[¥680]                        |
| 地点：[奥莱攀岩馆B区]                               |
|                                                     |
| **定金设置** (核心开关！)                           |
|                                                     |
| [✓] 本课程需支付定金  ← 开关按钮                    |
|                                                     |
| 定金金额：[¥200]  (或比例 [30%] ▾)                 |
|                                                     |
| **取消规则说明** (学员可见)：                       |
| [文本框] 提前24小时以上取消可退定金；24小时内不退。 |
|                                                     |
| [发布课程]                                         |
+-----------------------------------------------------+
```

#### 2.2.4 时间管理日历
- **功能**：可视化管理可预约时间段。
- **数据**：coach_availability集合。
- **云函数**：addAvailabilitySlot、removeAvailabilitySlot、getCoachSchedule。
- **跳转**：预约管理。

**原型图2：教练后台 - 时间管理日历（核心工具）**
(解决：手动标记空闲时间、避免重复预约)
```plaintext
+-----------------------------------------------------+
| **我的日历 - 2024年7月**                            |
|                                                     |
|  日  一  二  三  四  五  六                         |
|      1   2   3   4   5   6                          |
|   7 [8]  9  10  11  12  13   ← 选中7月8日（高亮）    |
|  14  15  16  17  18  19  20                        |
|                                                     |
+-----------------------------------------------------+
| **7月8日（周一）**                                  |
|                                                     |
|  [ 09:00 - 11:00 ]  ← 已添加时段 (可点击删除)        |
|  [ 14:00 - 16:00 ]                                  |
|                                                     |
|  **+ 添加可约时段**                                 |
|  开始时间：[14:00 ▾]  结束时间：[16:00 ▾]           |
|  [确认添加]                                         |
|                                                     |
|  **状态说明**：                                     |
|  · 白色格子：未设置                                 |
|  · 蓝色格子：已设可约时段                           |
|  · **灰色格子**：已被预约 (学员约后自动锁住)         |
+-----------------------------------------------------+
```

#### 2.2.5 预约管理
- **功能**：查看所有预约，确认/取消预约，管理学员。
- **数据**：appointments集合。
- **云函数**：getCoachSchedule、confirmAppointment、cancelAppointmentByCoach。
- **跳转**：时间管理日历。

**原型图9：预约管理**
(解决：高效处理预约+定金管理)
```plaintext
+-------------------------------------------------------------------+
| 预约管理                                                          |
+-------------------------------------------------------------------+
| [私教课] 7月10日 14:00-15:30  学员：王明  状态：待确认            |
| [确认预约] [取消预约]                                              |
+-------------------------------------------------------------------+
| [小班课] 7月12日 10:00-12:00  学员：李红  状态：已确认            |
| [联系学员]                                                        |
+-------------------------------------------------------------------+
| [私教课] 7月8日 16:00-17:30  学员：张三  状态：已取消             |
| [查看详情]                                                        |
+-------------------------------------------------------------------+
```
**设计要点：**  
- **操作直观**：待确认可确认/取消，已确认可联系学员。  
- **定金状态提示**：每条预约显示定金状态。  
- **学员信息可查**：便于沟通和管理。

#### 2.2.6 评价管理
- **功能**：查看学员评价，回复评价。
- **数据**：reviews集合。
- **云函数**：getCoachReviews、replyToReview。
- **跳转**：返回后台首页。

**原型图10：评价管理**
(解决：口碑维护+互动)
```plaintext
+-------------------------------------------------------------------+
| 评价管理                                                          |
+-------------------------------------------------------------------+
| [5★] 王明：讲解细致，进步很快！ [回复]                            |
| [4★] 匿名：希望多安排实战演练 [回复]                              |
+-------------------------------------------------------------------+
| [回复内容]（如有）                                                |
+-------------------------------------------------------------------+
```
**设计要点：**  
- **评价列表**：展示所有学员评价。  
- **一键回复**：教练可针对每条评价回复。  
- **回复内容展示**：提升互动感和专业形象。

#### 2.2.7 收入与提现
- **功能**：查看收入明细，申请提现。
- **数据**：transactions、withdrawals集合。
- **云函数**：getCoachEarnings、applyForWithdrawal。
- **跳转**：返回后台首页。

**原型图11：收入与提现**
(解决：收入明细透明+提现便捷)
```plaintext
+-------------------------------------------------------------------+
| 收入与提现                                                        |
+-------------------------------------------------------------------+
| 可提现余额：¥1200  [申请提现]                                     |
+-------------------------------------------------------------------+
| 收入明细                                                          |
| 7月10日 预约定金 +¥200                                            |
| 7月12日 小班课结算 +¥380                                          |
| 7月8日 预约取消 -¥200（定金退回）                                 |
+-------------------------------------------------------------------+
```
**设计要点：**  
- **余额与提现入口**：一目了然，便于操作。  
- **收入/提现明细**：每笔流水清晰可查。  
- **提现状态提示**：提现进度透明。

#### 2.2.8 营销工具
- **功能**：创建优惠券、生成分享链接、查看推荐关系。
- **数据**：coupons、referrals集合。
- **云函数**：createCoupon、redeemCoupon、generateShareLink。
- **跳转**：返回后台首页。

**原型图12：营销工具**
(解决：拉新促活+内容传播)
```plaintext
+-------------------------------------------------------------------+
| 营销工具                                                          |
+-------------------------------------------------------------------+
| [创建优惠券] [生成分享链接] [查看推荐关系]                         |
+-------------------------------------------------------------------+
| 优惠券列表                                                        |
| 新学员专享 ¥50 满减券  有效期：7.1-7.31  [核销]                   |
+-------------------------------------------------------------------+
| 推荐关系                                                          |
| 学员A 通过你推荐注册 7月10日                                      |
+-------------------------------------------------------------------+
| [分享小程序]                                                      |
+-------------------------------------------------------------------+
```
**设计要点：**  
- **多种营销工具入口**：优惠券、分享、推荐一站式管理。  
- **券与推荐明细**：便于教练追踪效果。  
- **一键分享**：促进内容传播和拉新。

---

## 3. 其他说明
- 所有页面需根据用户身份（教练/学员）动态展示入口。
- 页面间跳转建议使用微信小程序的 `navigateTo`、`redirectTo`、`switchTab` 等API。
- 重要数据交互均通过云函数实现，前端仅做展示和简单校验。
- 预约、支付、评价等关键流程需有异常处理和用户友好提示。
