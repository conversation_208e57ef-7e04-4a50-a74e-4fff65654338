<view class="login-container">
  <view class="header">
    <view class="logo-placeholder">🧗‍♂️</view>
    <text class="title">教练端登录</text>
    <text class="subtitle">请验证您的教练身份</text>
  </view>
  
  <view class="form-section">
    <view class="form-item">
      <text class="form-label">手机号</text>
      <input 
        class="form-input"
        placeholder="请输入注册的手机号"
        type="number"
        bindinput="onPhoneInput"
        value="{{phone}}"
        maxlength="11"
      />
    </view>
    
    <view class="form-item">
      <text class="form-label">密码</text>
      <input
        class="form-input"
        placeholder="请输入密码"
        type="password"
        bindinput="onPasswordInput"
        value="{{password}}"
      />
    </view>
  </view>
  
  <view class="tips-section">
    <text class="tips-title">温馨提示：</text>
    <text class="tips-content">• 请使用注册时的手机号登录</text>
    <text class="tips-content">• 如未注册教练账号，请联系管理员</text>
    <text class="tips-content">• 测试密码：123456</text>
  </view>
  
  <view class="action-section">
    <button 
      class="login-btn {{canLogin ? '' : 'disabled'}}"
      bindtap="handleLogin"
      disabled="{{!canLogin}}"
    >
      {{loading ? '登录中...' : '登录'}}
    </button>
    
    <button class="back-btn" bindtap="goBack">返回学员端</button>
  </view>
  
  <view class="contact-section">
    <text class="contact-text">遇到问题？</text>
    <button class="contact-btn" bindtap="contactAdmin">联系管理员</button>
  </view>
</view>
