/* 教练详情页样式 */
.coach-detail-container {
  background: #f6f6f6;
  min-height: 100vh;
  padding-bottom: 40rpx;
}
.coach-header {
  display: flex;
  justify-content: space-between;
  padding: 24rpx 32rpx 0 32rpx;
}
.back-btn, .share-btn {
  font-size: 28rpx;
  color: #007aff;
  background: none;
  border: none;
}
.cover-img {
  width: 100%;
  height: 260rpx;
  object-fit: cover;
  background: #eee;
}
.coach-info-block {
  background: #fff;
  margin: -60rpx 24rpx 0 24rpx;
  border-radius: 18rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.04);
  padding: 32rpx 24rpx 24rpx 24rpx;
  position: relative;
  z-index: 2;
}
.avatar {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  position: absolute;
  top: -48rpx;
  left: 32rpx;
  border: 4rpx solid #fff;
  background: #eee;
}
.name-rating {
  margin-left: 140rpx;
  margin-bottom: 12rpx;
}
.name {
  font-size: 36rpx;
  font-weight: 600;
  color: #222;
  margin-right: 16rpx;
}
.rating {
  font-size: 26rpx;
  color: #ffb400;
}
.ability-tags {
  margin-bottom: 16rpx;
}
.section-subtitle {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}
.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}
.tag {
  background: #f0f0f0;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  color: #666;
}
.info-item {
  font-size: 24rpx;
  color: #888;
  margin-bottom: 8rpx;
  display: flex;
}
.info-label {
  color: #666;
  min-width: 120rpx;
}
.info-value {
  color: #333;
  flex: 1;
}
.deposit-block {
  background: #fffbe0;
  margin: 24rpx 24rpx 0 24rpx;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 26rpx;
  color: #b8860b;
  border-left: 4rpx solid #ffb400;
}
.deposit-item {
  margin-bottom: 12rpx;
  display: flex;
  align-items: center;
}
.deposit-icon {
  margin-right: 8rpx;
  font-size: 24rpx;
}
.review-block, .course-block {
  background: #fff;
  margin: 24rpx 24rpx 0 24rpx;
  border-radius: 12rpx;
  padding: 24rpx;
}
.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #222;
  margin-bottom: 16rpx;
  display: block;
}
.review-item {
  font-size: 24rpx;
  color: #444;
  margin-bottom: 8rpx;
}
.review-rating {
  color: #ffb400;
}
.all-reviews-btn {
  margin-top: 8rpx;
  color: #007aff;
  background: none;
  border: none;
  font-size: 26rpx;
}
.course-item {
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e9ecef;
  transition: all 0.2s;
}

.course-item:last-child {
  margin-bottom: 0;
}

.course-item:active {
  background: #e9ecef;
  transform: scale(0.99);
}
.course-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.course-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
  flex: 1;
}

.course-duration {
  font-size: 22rpx;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  margin: 0 12rpx;
}

.course-price {
  font-size: 32rpx;
  color: #e54545;
  font-weight: 700;
}

.course-price::before {
  content: '¥';
  font-size: 24rpx;
}
.course-intro {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 16rpx;
  background: rgba(255, 255, 255, 0.8);
  padding: 12rpx;
  border-radius: 8rpx;
}
.book-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border-radius: 20rpx;
  padding: 16rpx 32rpx;
  font-size: 24rpx;
  font-weight: 500;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.book-btn:active {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
}

.book-btn::before {
  content: '📅';
  font-size: 20rpx;
}

.course-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 8rpx;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 16rpx 20rpx;
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
  display: flex;
  gap: 12rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0,0,0,0.1);
  border-top: 1rpx solid #f0f0f0;
}

.schedule-btn, .contact-btn, .appointment-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 12rpx;
  border: none;
  font-size: 24rpx;
  font-weight: 500;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4rpx;
  transition: all 0.2s;
}

.schedule-btn {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  color: #1976d2;
  border: 1rpx solid #e3f2fd;
}

.schedule-btn:active {
  background: linear-gradient(135deg, #bbdefb 0%, #90caf9 100%);
  transform: scale(0.98);
}

.contact-btn {
  background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
  color: #7b1fa2;
  border: 1rpx solid #f3e5f5;
}

.contact-btn:active {
  background: linear-gradient(135deg, #e1bee7 0%, #ce93d8 100%);
  transform: scale(0.98);
}

.appointment-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.appointment-btn:active {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
}

.btn-icon {
  font-size: 28rpx;
  line-height: 1;
}

.btn-text {
  font-size: 22rpx;
  line-height: 1;
}