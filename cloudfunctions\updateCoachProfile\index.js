const cloud = require('wx-server-sdk')
cloud.init()
const db = cloud.database()

exports.main = async (event, context) => {
  const { profileData } = event
  
  // 获取用户openid
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  if (!profileData) {
    return {
      success: false,
      error: '参数不完整'
    }
  }
  
  try {
    // 获取教练信息
    const coachRes = await db.collection('coaches')
      .where({ openid: openid })
      .get()
    
    if (coachRes.data.length === 0) {
      return {
        success: false,
        error: '教练信息不存在'
      }
    }
    
    const coach = coachRes.data[0]
    const coachId = coach._id
    
    // 准备更新数据
    const updateData = {
      name: profileData.name,
      avatar: profileData.avatar,
      gender: profileData.gender,
      city: profileData.city,
      gym: profileData.gym,
      boulderingLevel: profileData.boulderingLevel,
      leadLevel: profileData.leadLevel,
      certifications: profileData.certifications,
      specialty: profileData.specialty,
      style: profileData.style,
      intro: profileData.intro,
      courseName: profileData.courseName,
      price: parseFloat(profileData.price) || 0,
      needDeposit: profileData.needDeposit,
      gallery: profileData.gallery,
      contacts: profileData.contacts || {
        wechat: '',
        xiaohongshu: '',
        videoAccount: '',
        contactPhone: ''
      },
      updatedAt: new Date()
    }
    
    // 更新教练信息
    await db.collection('coaches').doc(coachId).update({
      data: updateData
    })
    
    return {
      success: true,
      message: '个人信息更新成功'
    }
  } catch (error) {
    console.error('更新教练信息失败：', error)
    return {
      success: false,
      error: error.message
    }
  }
}
