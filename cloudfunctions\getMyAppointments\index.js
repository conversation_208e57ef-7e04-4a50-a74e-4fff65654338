const cloud = require('wx-server-sdk')
cloud.init()
const db = cloud.database()

exports.main = async (event, context) => {
  // 获取用户openid
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID

  if (!openid) {
    return {
      success: false,
      error: '用户身份验证失败'
    }
  }

  try {
    // 查询用户的课程记录
    const appointmentsRes = await db.collection('appointments')
      .where({
        studentId: openid
      })
      .orderBy('createdAt', 'desc')
      .get()

    const appointments = appointmentsRes.data || []

    // 获取教练信息来补充预约数据
    const enrichedAppointments = await Promise.all(
      appointments.map(async (appointment) => {
        try {
          const coachRes = await db.collection('coaches').doc(appointment.coachId).get()
          const coach = coachRes.data || {}

          return {
            ...appointment,
            coachName: coach.name || '未知教练',
            location: coach.gym || '待确认'
          }
        } catch (error) {
          console.error('获取教练信息失败：', error)
          return {
            ...appointment,
            coachName: '未知教练',
            location: '待确认'
          }
        }
      })
    )

    return {
      success: true,
      data: enrichedAppointments
    }
  } catch (error) {
    console.error('获取预约列表失败：', error)
    return {
      success: false,
      error: error.message
    }
  }
}