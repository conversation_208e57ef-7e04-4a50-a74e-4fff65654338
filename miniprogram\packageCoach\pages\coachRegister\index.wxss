/* 教练注册页面样式 */
.register-container {
  min-height: 100vh;
  background: #f5f6fa;
  padding-bottom: 120rpx;
}

.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 32rpx 40rpx 32rpx;
  text-align: center;
  color: #fff;
}

.title {
  font-size: 48rpx;
  font-weight: 700;
  margin-bottom: 12rpx;
  display: block;
}

.subtitle {
  font-size: 26rpx;
  opacity: 0.9;
  display: block;
}

.form-section {
  background: #fff;
  margin: 24rpx 24rpx 0 24rpx;
  border-radius: 16rpx;
  padding: 32rpx 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  padding-bottom: 12rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.section-desc {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 24rpx;
  line-height: 1.4;
}

.form-item {
  margin-bottom: 32rpx;
  position: relative;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 12rpx;
  display: block;
}

.form-input, .form-textarea {
  width: 100%;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 20rpx 16rpx;
  font-size: 28rpx;
  color: #333;
  transition: all 0.2s;
  box-sizing: border-box;
}

.form-input:focus, .form-textarea:focus {
  border-color: #667eea;
  background: #fff;
  box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
}

.form-textarea {
  min-height: 120rpx;
  resize: none;
}

.char-count {
  position: absolute;
  right: 12rpx;
  bottom: 8rpx;
  font-size: 22rpx;
  color: #999;
}

.agreement-section {
  margin: 32rpx 24rpx;
  padding: 24rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.05);
}

.agreement-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.agreement-text {
  font-size: 26rpx;
  color: #666;
}

.agreement-link {
  font-size: 26rpx;
  color: #667eea;
  text-decoration: underline;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20rpx 24rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  display: flex;
  gap: 16rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0,0,0,0.1);
}

.cancel-btn, .submit-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
  transition: all 0.2s;
}

.cancel-btn {
  background: #f8f9fa;
  color: #666;
  border: 2rpx solid #e9ecef;
}

.cancel-btn:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.submit-btn {
  background: #ccc;
  color: #999;
}

.submit-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.submit-btn.active:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
}
