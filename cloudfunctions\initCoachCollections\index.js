const cloud = require('wx-server-sdk')
cloud.init()
const db = cloud.database()

exports.main = async (event, context) => {
  try {
    // 获取用户openid
    const wxContext = cloud.getWXContext()
    const openid = wxContext.OPENID
    
    // 获取教练信息
    const coachRes = await db.collection('coaches')
      .where({ openid: openid })
      .get()
    
    if (coachRes.data.length === 0) {
      return {
        success: false,
        error: '教练信息不存在'
      }
    }
    
    const coach = coachRes.data[0]
    const coachId = coach._id
    
    const results = []
    
    // 尝试创建 coach_courses 集合
    try {
      // 先尝试添加一个临时记录来创建集合
      const tempCourseRes = await db.collection('coach_courses').add({
        data: {
          coachId: coachId,
          name: '临时课程',
          type: '私教课',
          duration: 90,
          price: 680,
          needDeposit: false,
          deposit: 0,
          description: '这是一个临时课程，用于创建集合',
          outline: '',
          targetAudience: '',
          status: 'inactive',
          _temp: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      })
      
      // 立即删除临时记录
      await db.collection('coach_courses').doc(tempCourseRes._id).remove()
      
      results.push('coach_courses 集合创建成功')
    } catch (error) {
      console.error('创建 coach_courses 集合失败：', error)
      results.push(`coach_courses 集合创建失败: ${error.message}`)
    }
    
    // 尝试创建 coach_availability 集合
    try {
      // 先尝试添加一个临时记录来创建集合
      const tempAvailRes = await db.collection('coach_availability').add({
        data: {
          coachId: coachId,
          date: '2024-01-01',
          startTime: '09:00',
          endTime: '10:30',
          status: 'available',
          appointmentId: null,
          _temp: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      })
      
      // 立即删除临时记录
      await db.collection('coach_availability').doc(tempAvailRes._id).remove()
      
      results.push('coach_availability 集合创建成功')
    } catch (error) {
      console.error('创建 coach_availability 集合失败：', error)
      results.push(`coach_availability 集合创建失败: ${error.message}`)
    }
    
    return {
      success: true,
      message: '教练端数据库集合初始化完成',
      results: results
    }
  } catch (error) {
    console.error('初始化教练端集合失败：', error)
    return {
      success: false,
      error: error.message
    }
  }
}
