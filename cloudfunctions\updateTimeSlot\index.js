const cloud = require('wx-server-sdk')
cloud.init()
const db = cloud.database()

exports.main = async (event, context) => {
  const { slotId, startTime, endTime } = event
  
  // 获取用户openid
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  if (!slotId || !startTime || !endTime) {
    return {
      success: false,
      error: '参数不完整'
    }
  }
  
  // 验证时间格式
  if (startTime >= endTime) {
    return {
      success: false,
      error: '开始时间必须早于结束时间'
    }
  }
  
  try {
    // 获取教练信息
    const coachRes = await db.collection('coaches')
      .where({ openid: openid })
      .get()
    
    if (coachRes.data.length === 0) {
      return {
        success: false,
        error: '教练信息不存在'
      }
    }
    
    const coach = coachRes.data[0]
    const coachId = coach._id
    
    // 获取要更新的时间段信息
    const slotRes = await db.collection('coach_availability').doc(slotId).get()
    if (!slotRes.data) {
      return {
        success: false,
        error: '时间段不存在'
      }
    }
    
    const slot = slotRes.data
    
    // 验证权限
    if (slot.coachId !== coachId) {
      return {
        success: false,
        error: '无权限操作此时间段'
      }
    }
    
    // 检查时间段状态
    if (slot.status !== 'available') {
      return {
        success: false,
        error: '只能编辑可预约状态的时间段'
      }
    }
    
    // 检查时间冲突（排除当前时间段）
    const conflictRes = await db.collection('coach_availability')
      .where({
        coachId: coachId,
        date: slot.date,
        _id: db.command.neq(slotId),
        startTime: db.command.lt(endTime),
        endTime: db.command.gt(startTime)
      })
      .get()
    
    if (conflictRes.data.length > 0) {
      return {
        success: false,
        error: '该时间段与现有安排冲突'
      }
    }
    
    // 更新时间段
    await db.collection('coach_availability').doc(slotId).update({
      data: {
        startTime: startTime,
        endTime: endTime,
        updatedAt: new Date()
      }
    })
    
    return {
      success: true,
      message: '时间段更新成功'
    }
  } catch (error) {
    console.error('更新时间段失败：', error)
    return {
      success: false,
      error: error.message
    }
  }
}
