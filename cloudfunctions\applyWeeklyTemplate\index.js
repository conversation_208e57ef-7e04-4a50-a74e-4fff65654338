const cloud = require('wx-server-sdk')
cloud.init()
const db = cloud.database()

exports.main = async (event, context) => {
  const { weekdays, timeSlots, startDate, endDate } = event
  
  // 获取用户openid
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  if (!weekdays || !timeSlots || !startDate || !endDate) {
    return {
      success: false,
      error: '参数不完整'
    }
  }
  
  try {
    // 获取教练信息
    const coachRes = await db.collection('coaches')
      .where({ openid: openid })
      .get()
    
    if (coachRes.data.length === 0) {
      return {
        success: false,
        error: '教练信息不存在'
      }
    }
    
    const coach = coachRes.data[0]
    const coachId = coach._id
    
    // 生成日期范围内的所有日期
    const dates = []
    const start = new Date(startDate)
    const end = new Date(endDate)
    
    for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
      const weekday = d.getDay()
      if (weekdays.includes(weekday)) {
        dates.push(d.toISOString().split('T')[0])
      }
    }
    
    let addedCount = 0
    
    // 为每个日期和时间段组合添加可预约时间
    for (const date of dates) {
      for (const timeSlot of timeSlots) {
        try {
          // 检查时间冲突
          const conflictRes = await db.collection('coach_availability')
            .where({
              coachId: coachId,
              date: date,
              startTime: db.command.lt(timeSlot.endTime),
              endTime: db.command.gt(timeSlot.startTime)
            })
            .get()
          
          if (conflictRes.data.length === 0) {
            // 没有冲突，添加时间段
            await db.collection('coach_availability').add({
              data: {
                coachId: coachId,
                date: date,
                startTime: timeSlot.startTime,
                endTime: timeSlot.endTime,
                status: 'available',
                appointmentId: null,
                createdAt: new Date(),
                updatedAt: new Date()
              }
            })
            addedCount++
          }
        } catch (error) {
          console.error(`添加时间段失败 ${date} ${timeSlot.startTime}-${timeSlot.endTime}:`, error)
        }
      }
    }
    
    return {
      success: true,
      message: `成功应用模板，添加了 ${addedCount} 个时间段`,
      addedCount: addedCount
    }
  } catch (error) {
    console.error('应用每周模板失败：', error)
    return {
      success: false,
      error: error.message
    }
  }
}
