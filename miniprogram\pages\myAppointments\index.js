Page({
  data: {
    currentTab: 'ongoing',
    appointments: [],
    filteredAppointments: [],
    loading: false,
    errorMsg: '',
    tabNames: {
      ongoing: '进行中',
      completed: '已完成',
      cancelled: '已取消'
    }
  },

  onLoad() {
    this.loadMyAppointments();
  },

  onShow() {
    // 每次显示页面时刷新数据
    this.loadMyAppointments();
  },

  async loadMyAppointments() {
    this.setData({ loading: true, errorMsg: '' });

    try {
      const res = await wx.cloud.callFunction({
        name: 'getMyAppointments',
        data: {}
      });

      if (res.result.success) {
        const appointments = this.processAppointments(res.result.data || []);
        this.setData({
          appointments: appointments,
          loading: false
        });
        this.filterAppointments();
      } else {
        this.setData({
          loading: false,
          errorMsg: res.result.error || '加载失败'
        });
      }
    } catch (error) {
      console.error('获取预约列表失败：', error);
      this.setData({
        loading: false,
        errorMsg: '网络错误，请重试'
      });
    }
  },

  processAppointments(appointments) {
    return appointments.map(item => {
      // 处理状态文本
      const statusMap = {
        '待确认': { text: '待确认', class: 'pending' },
        '已确认': { text: '已确认', class: 'confirmed' },
        '已完成': { text: '已完成', class: 'completed' },
        '已取消': { text: '已取消', class: 'cancelled' }
      };

      const statusInfo = statusMap[item.status] || { text: item.status, class: 'unknown' };

      // 处理定金状态文本
      const depositStatusMap = {
        '未支付': '未支付',
        '已支付': '已支付',
        '已退回': '已退回',
        '已结算': '已结算',
        '无需支付': '无需支付'
      };

      // 确定可用操作
      const actions = [];
      if (item.status === '待确认') {
        actions.push({ action: 'cancel', text: '取消预约', type: 'secondary' });
        actions.push({ action: 'contact', text: '联系教练', type: 'primary' });
      } else if (item.status === '已确认') {
        actions.push({ action: 'contact', text: '联系教练', type: 'primary' });
      } else if (item.status === '已完成') {
        actions.push({ action: 'review', text: '去评价', type: 'primary' });
        actions.push({ action: 'rebook', text: '再次预约', type: 'secondary' });
      } else if (item.status === '已取消') {
        actions.push({ action: 'rebook', text: '再次预约', type: 'primary' });
      }

      return {
        ...item,
        statusText: statusInfo.text,
        statusClass: statusInfo.class,
        depositStatusText: depositStatusMap[item.depositStatus] || item.depositStatus,
        coachName: item.coachName || '未知教练',
        location: item.location || '待确认',
        actions: actions
      };
    });
  },

  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({ currentTab: tab });
    this.filterAppointments();
  },

  filterAppointments() {
    const { appointments, currentTab } = this.data;
    let filtered = [];

    switch (currentTab) {
      case 'ongoing':
        filtered = appointments.filter(item =>
          item.status === '待确认' || item.status === '已确认'
        );
        break;
      case 'completed':
        filtered = appointments.filter(item => item.status === '已完成');
        break;
      case 'cancelled':
        filtered = appointments.filter(item => item.status === '已取消');
        break;
    }

    this.setData({ filteredAppointments: filtered });
  },

  handleAction(e) {
    const { action, id, coachId } = e.currentTarget.dataset;

    switch (action) {
      case 'cancel':
        this.cancelAppointment(id);
        break;
      case 'contact':
        this.contactCoach(coachId);
        break;
      case 'review':
        this.goToReview(id);
        break;
      case 'rebook':
        this.rebookAppointment(coachId);
        break;
    }
  },

  cancelAppointment(appointmentId) {
    wx.showModal({
      title: '确认取消',
      content: '确定要取消这个预约吗？',
      success: (res) => {
        if (res.confirm) {
          // TODO: 调用取消预约的云函数
          wx.showToast({ title: '取消成功', icon: 'success' });
          this.loadMyAppointments();
        }
      }
    });
  },

  contactCoach(coachId) {
    wx.navigateTo({
      url: `/pages/message/index?coachId=${coachId}`
    });
  },

  goToReview(appointmentId) {
    wx.navigateTo({
      url: `/pages/reviewSubmit/index?appointmentId=${appointmentId}`
    });
  },

  rebookAppointment(coachId) {
    wx.navigateTo({
      url: `/pages/appointment/index?coachId=${coachId}`
    });
  },

  goToDetail(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/appointmentDetail/index?id=${id}`
    });
  }
});