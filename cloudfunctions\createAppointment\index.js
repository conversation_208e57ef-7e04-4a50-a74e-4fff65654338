const cloud = require('wx-server-sdk')
cloud.init()
const db = cloud.database()

exports.main = async (event, context) => {
  const {
    coachId,
    studentId,
    courseId,
    timeSlot,
    studentName,
    studentPhone,
    remark
  } = event

  // 获取用户openid
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID

  if (!coachId || !courseId || !timeSlot || !studentName || !studentPhone) {
    return {
      success: false,
      error: '参数不完整'
    }
  }

  try {
    // 获取教练信息
    const coachRes = await db.collection('coaches').doc(coachId).get()
    if (!coachRes.data) {
      return {
        success: false,
        error: '教练不存在'
      }
    }

    const coach = coachRes.data

    // 检查时间段是否已被预约
    const slotKey = `${timeSlot.date}_${timeSlot.start}_${timeSlot.end}`
    const existingAppointment = await db.collection('appointments')
      .where({
        coachId: coachId,
        'timeSlot.date': timeSlot.date,
        'timeSlot.start': timeSlot.start,
        'timeSlot.end': timeSlot.end,
        status: db.RegExp({
          regexp: '^(待确认|已确认)$',
          options: 'i'
        })
      })
      .count()

    if (existingAppointment.total > 0) {
      return {
        success: false,
        error: '该时间段已被预约'
      }
    }

    // 计算定金
    const price = coach.price || 680
    const depositAmount = coach.needDeposit ? Math.round(price * 0.3) : 0

    // 创建预约记录
    const appointmentData = {
      coachId: coachId,
      studentId: openid,
      courseId: courseId,
      courseTitle: coach.courseName || '私教课',
      timeSlot: timeSlot,
      status: '待确认',
      deposit: depositAmount,
      depositStatus: depositAmount > 0 ? '未支付' : '无需支付',
      studentName: studentName,
      studentPhone: studentPhone,
      remark: remark || '',
      price: price,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    const res = await db.collection('appointments').add({
      data: appointmentData
    })

    return {
      success: true,
      data: {
        appointmentId: res._id,
        needDeposit: coach.needDeposit,
        depositAmount: depositAmount,
        coachWechat: coach.wechat,
        appointment: appointmentData
      }
    }
  } catch (error) {
    console.error('创建课程记录失败：', error)
    return {
      success: false,
      error: error.message
    }
  }
}