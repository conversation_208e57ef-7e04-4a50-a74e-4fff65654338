/* 教练评价管理页样式 */
.reviews-container {
  background: #f6f6f6;
  min-height: 100vh;
}

.header {
  background: #667eea;
  padding: 32rpx;
  text-align: center;
}

.title {
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
}

.stats-section {
  background: #fff;
  margin: 16rpx;
  border-radius: 12rpx;
  padding: 32rpx;
}

.stats-grid {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 36rpx;
  font-weight: 600;
  color: #667eea;
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

.reviews-list {
  padding: 0 16rpx;
}

.loading, .error-msg {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding: 80rpx 0;
}

.error-msg {
  color: #e54545;
}

.empty-tip {
  text-align: center;
  padding: 80rpx 40rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.review-card {
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.student-info {
  flex: 1;
}

.student-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 4rpx;
}

.review-time {
  font-size: 22rpx;
  color: #999;
}

.rating-stars {
  display: flex;
  gap: 4rpx;
}

.star {
  font-size: 24rpx;
  color: #ddd;
}

.star.active {
  color: #ffa500;
}

.review-content {
  margin-bottom: 16rpx;
}

.content-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.6;
}

.review-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.tag {
  background: #f0f0f0;
  color: #666;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
}

.review-course {
  margin-bottom: 16rpx;
}

.course-info {
  font-size: 24rpx;
  color: #999;
}

.review-images {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.review-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
}

.review-actions {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16rpx;
}

.reply-btn {
  background: #667eea;
  color: #fff;
  border: none;
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
}

.coach-reply {
  background: #f8f9fa;
  border-radius: 8rpx;
  padding: 16rpx;
  border-left: 4rpx solid #667eea;
}

.reply-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.reply-label {
  font-size: 24rpx;
  font-weight: 600;
  color: #667eea;
}

.reply-time {
  font-size: 22rpx;
  color: #999;
}

.reply-content {
  font-size: 26rpx;
  color: #333;
  line-height: 1.6;
}
