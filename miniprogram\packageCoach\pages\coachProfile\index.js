Page({
  data: {
    profileData: {
      name: '',
      avatar: '',
      gender: '男',
      city: '',
      gym: '',
      boulderingLevel: '',
      leadLevel: '',
      certifications: [],
      specialty: [],
      style: '',
      intro: '',
      courseName: '',
      price: 0,
      needDeposit: false,
      gallery: [],
      // 联系方式
      contacts: {
        wechat: '',
        xiaohongshu: '',
        videoAccount: '',
        contactPhone: ''
      }
    },
    certificationsText: '',
    specialtyText: '',
    saving: false
  },

  onLoad() {
    this.loadProfile();
  },

  async loadProfile() {
    wx.showLoading({ title: '加载中...' });
    
    try {
      const res = await wx.cloud.callFunction({
        name: 'coachAuth',
        data: {
          action: 'getCoachInfo'
        }
      });
      
      if (res.result.success) {
        const coach = res.result.data.coach;
        const profileData = {
          name: coach.name || '',
          avatar: coach.avatar || 'cloud://miniprogram-15-7g0tuaph2c2c4e96.6d69-miniprogram-15-7g0tuaph2c2c4e96-**********/default-avatar.png',
          gender: coach.gender || '男',
          city: coach.city || '',
          gym: coach.gym || '',
          boulderingLevel: coach.boulderingLevel || '',
          leadLevel: coach.leadLevel || '',
          certifications: coach.certifications || [],
          specialty: coach.specialty || [],
          style: coach.style || '',
          intro: coach.intro || '',
          courseName: coach.courseName || '',
          price: coach.price || 0,
          needDeposit: coach.needDeposit || false,
          gallery: coach.gallery || [],
          contacts: coach.contacts || {
            wechat: '',
            xiaohongshu: '',
            videoAccount: '',
            contactPhone: ''
          }
        };
        
        this.setData({
          profileData: profileData,
          certificationsText: profileData.certifications.join(', '),
          specialtyText: profileData.specialty.join(', ')
        });
      } else {
        wx.showToast({
          title: res.result.error || '加载失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('加载个人信息失败：', error);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    const value = e.detail.value;
    this.setData({
      [`profileData.${field}`]: value
    });
  },

  onGenderChange(e) {
    const value = e.currentTarget.dataset.value;
    this.setData({
      'profileData.gender': value
    });
  },

  onCertificationsChange(e) {
    const text = e.detail.value;
    const certifications = text.split(',').map(item => item.trim()).filter(item => item);
    this.setData({
      certificationsText: text,
      'profileData.certifications': certifications
    });
  },

  onSpecialtyChange(e) {
    const text = e.detail.value;
    const specialty = text.split(',').map(item => item.trim()).filter(item => item);
    this.setData({
      specialtyText: text,
      'profileData.specialty': specialty
    });
  },

  onDepositChange(e) {
    this.setData({
      'profileData.needDeposit': e.detail.value
    });
  },

  onContactChange(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    this.setData({
      [`profileData.contacts.${field}`]: value
    });
  },

  uploadAvatar() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: async (res) => {
        const tempFilePath = res.tempFilePaths[0];
        
        wx.showLoading({ title: '上传中...' });
        
        try {
          const cloudPath = `coach-avatars/${Date.now()}-${Math.random().toString(36).substr(2, 9)}.jpg`;
          const uploadRes = await wx.cloud.uploadFile({
            cloudPath: cloudPath,
            filePath: tempFilePath
          });
          
          this.setData({
            'profileData.avatar': uploadRes.fileID
          });
          
          wx.showToast({
            title: '上传成功',
            icon: 'success'
          });
        } catch (error) {
          console.error('上传头像失败：', error);
          wx.showToast({
            title: '上传失败，请重试',
            icon: 'none'
          });
        } finally {
          wx.hideLoading();
        }
      }
    });
  },

  addGalleryImage() {
    const currentCount = this.data.profileData.gallery.length;
    const maxCount = 9 - currentCount;
    
    wx.chooseImage({
      count: maxCount,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: async (res) => {
        const tempFilePaths = res.tempFilePaths;
        
        wx.showLoading({ title: '上传中...' });
        
        try {
          const uploadPromises = tempFilePaths.map(async (filePath, index) => {
            const cloudPath = `coach-gallery/${Date.now()}-${index}-${Math.random().toString(36).substr(2, 9)}.jpg`;
            const uploadRes = await wx.cloud.uploadFile({
              cloudPath: cloudPath,
              filePath: filePath
            });
            return uploadRes.fileID;
          });
          
          const fileIDs = await Promise.all(uploadPromises);
          const newGallery = [...this.data.profileData.gallery, ...fileIDs];
          
          this.setData({
            'profileData.gallery': newGallery
          });
          
          wx.showToast({
            title: '上传成功',
            icon: 'success'
          });
        } catch (error) {
          console.error('上传图片失败：', error);
          wx.showToast({
            title: '上传失败，请重试',
            icon: 'none'
          });
        } finally {
          wx.hideLoading();
        }
      }
    });
  },

  deleteGalleryImage(e) {
    const index = e.currentTarget.dataset.index;
    const gallery = [...this.data.profileData.gallery];
    gallery.splice(index, 1);
    
    this.setData({
      'profileData.gallery': gallery
    });
  },

  previewImage(e) {
    const url = e.currentTarget.dataset.url;
    wx.previewImage({
      current: url,
      urls: this.data.profileData.gallery
    });
  },

  onAvatarError() {
    // 头像加载失败时使用默认头像
    this.setData({
      'profileData.avatar': 'cloud://miniprogram-15-7g0tuaph2c2c4e96.6d69-miniprogram-15-7g0tuaph2c2c4e96-**********/default-avatar.png'
    });
  },

  async saveProfile() {
    const { profileData } = this.data;
    
    // 基本验证
    if (!profileData.name.trim()) {
      wx.showToast({
        title: '请输入姓名',
        icon: 'none'
      });
      return;
    }
    
    if (!profileData.gym.trim()) {
      wx.showToast({
        title: '请输入所属岩馆',
        icon: 'none'
      });
      return;
    }
    
    this.setData({ saving: true });
    
    try {
      const res = await wx.cloud.callFunction({
        name: 'updateCoachProfile',
        data: {
          profileData: profileData
        }
      });
      
      if (res.result.success) {
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });
        
        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        wx.showToast({
          title: res.result.error || '保存失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('保存个人信息失败：', error);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ saving: false });
    }
  }
});
