/* 教练登录页样式 */
.login-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  padding: 60rpx 40rpx;
}

.header {
  text-align: center;
  margin-bottom: 80rpx;
}

.logo-placeholder {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 32rpx;
  font-size: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255,255,255,0.2);
  border-radius: 50%;
}

.title {
  font-size: 48rpx;
  font-weight: 600;
  color: #fff;
  display: block;
  margin-bottom: 16rpx;
}

.subtitle {
  font-size: 28rpx;
  color: rgba(255,255,255,0.8);
  display: block;
}

.form-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 48rpx 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
}

.form-item {
  margin-bottom: 32rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 16rpx;
}

.form-input {
  width: 100%;
  background: #f8f8f8;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 28rpx;
  border: 2rpx solid #f8f8f8;
  transition: border-color 0.2s;
}

.form-input:focus {
  border-color: #667eea;
  background: #fff;
}

.verify-row {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.verify-input {
  flex: 1;
}

.send-code-btn {
  background: #667eea;
  color: #fff;
  border-radius: 12rpx;
  padding: 24rpx 32rpx;
  font-size: 24rpx;
  border: none;
  white-space: nowrap;
}

.send-code-btn.disabled {
  background: #ccc;
  color: #999;
}

.tips-section {
  background: rgba(255,255,255,0.9);
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 32rpx;
}

.tips-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 12rpx;
}

.tips-content {
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
  display: block;
  margin-bottom: 8rpx;
}

.action-section {
  margin-bottom: 32rpx;
}

.login-btn {
  width: 100%;
  background: #fff;
  color: #667eea;
  border-radius: 24rpx;
  padding: 24rpx 0;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  margin-bottom: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
}

.login-btn.disabled {
  background: rgba(255,255,255,0.5);
  color: rgba(102,126,234,0.5);
}

.back-btn {
  width: 100%;
  background: transparent;
  color: #fff;
  border: 2rpx solid rgba(255,255,255,0.5);
  border-radius: 24rpx;
  padding: 20rpx 0;
  font-size: 28rpx;
}

.contact-section {
  text-align: center;
}

.contact-text {
  font-size: 24rpx;
  color: rgba(255,255,255,0.8);
  margin-right: 16rpx;
}

.contact-btn {
  background: transparent;
  color: #fff;
  border: none;
  font-size: 24rpx;
  text-decoration: underline;
  padding: 0;
}
