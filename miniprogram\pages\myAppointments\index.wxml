<view class="appointments-container">
  <view class="header">
    <text class="title">我的记录</text>
  </view>

  <view class="tabs">
    <button
      class="tab-btn {{currentTab === 'ongoing' ? 'active' : ''}}"
      bindtap="switchTab"
      data-tab="ongoing"
    >
      进行中
    </button>
    <button
      class="tab-btn {{currentTab === 'completed' ? 'active' : ''}}"
      bindtap="switchTab"
      data-tab="completed"
    >
      已完成
    </button>
    <button
      class="tab-btn {{currentTab === 'cancelled' ? 'active' : ''}}"
      bindtap="switchTab"
      data-tab="cancelled"
    >
      已取消
    </button>
  </view>

  <view class="appointments-list">
    <view wx:if="{{loading}}" class="loading">加载中...</view>
    <view wx:elif="{{errorMsg}}" class="error-msg">{{errorMsg}}</view>
    <view wx:elif="{{filteredAppointments.length === 0}}" class="empty-tip">
      <text class="empty-text">暂无{{tabNames[currentTab]}}的预约</text>
      <navigator url="/pages/index/index" class="go-book-btn">去预约</navigator>
    </view>
    <block wx:else>
      <view
        wx:for="{{filteredAppointments}}"
        wx:key="_id"
        class="appointment-card"
        bindtap="goToDetail"
        data-id="{{item._id}}"
      >
        <view class="card-header">
          <text class="course-title">[{{item.courseTitle}}]</text>
          <text class="status-tag status-{{item.status}}">{{item.statusText}}</text>
        </view>

        <view class="card-content">
          <view class="info-row">
            <text class="label">教练：</text>
            <text class="value">{{item.coachName}}</text>
          </view>
          <view class="info-row">
            <text class="label">时间：</text>
            <text class="value">{{item.timeSlot.date}} {{item.timeSlot.start}}-{{item.timeSlot.end}}</text>
          </view>
          <view class="info-row">
            <text class="label">地点：</text>
            <text class="value">{{item.location}}</text>
          </view>
          <view class="info-row" wx:if="{{item.deposit > 0}}">
            <text class="label">定金：</text>
            <text class="value deposit-status">¥{{item.deposit}} ({{item.depositStatusText}})</text>
          </view>
        </view>

        <view class="card-actions" wx:if="{{item.actions.length > 0}}">
          <block wx:for="{{item.actions}}" wx:key="*this" wx:for-item="action">
            <button
              class="action-btn {{action.type}}"
              bindtap="handleAction"
              data-action="{{action.action}}"
              data-id="{{item._id}}"
              data-coach-id="{{item.coachId}}"
            >
              {{action.text}}
            </button>
          </block>
        </view>
      </view>
    </block>
  </view>
</view>