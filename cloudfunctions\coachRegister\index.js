const cloud = require('wx-server-sdk')
cloud.init()
const db = cloud.database()

exports.main = async (event, context) => {
  const {
    name,
    phone,
    password,
    city,
    gym,
    boulderingLevel,
    leadLevel,
    specialty,
    intro,
    wechat,
    xiaohongshu,
    videoAccount,
    contactPhone
  } = event
  
  // 获取用户openid
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  try {
    // 检查手机号是否已注册
    const existingCoach = await db.collection('coaches')
      .where({
        phone: phone
      })
      .get()
    
    if (existingCoach.data.length > 0) {
      return {
        success: false,
        error: '该手机号已注册'
      }
    }
    
    // 检查openid是否已注册
    const existingOpenid = await db.collection('coaches')
      .where({
        openid: openid
      })
      .get()
    
    if (existingOpenid.data.length > 0) {
      return {
        success: false,
        error: '该微信账号已注册'
      }
    }
    
    // 创建教练记录
    const coachData = {
      openid: openid,
      name: name,
      phone: phone,
      password: password, // 实际项目中应该加密存储
      city: city,
      gym: gym || '',
      boulderingLevel: boulderingLevel || '',
      leadLevel: leadLevel || '',
      specialty: specialty ? specialty.split(',').map(s => s.trim()) : [],
      intro: intro || '',

      // 联系方式
      contacts: {
        wechat: wechat || '',
        xiaohongshu: xiaohongshu || '',
        videoAccount: videoAccount || '',
        contactPhone: contactPhone || ''
      },

      // 默认值
      avatar: '',
      gender: '男',
      certifications: [],
      style: '',
      courseName: '',
      price: 0,
      needDeposit: false,
      gallery: [],
      rating: 0,
      reviewCount: 0,
      
      // 状态信息
      status: 'pending', // pending: 待审核, approved: 已通过, rejected: 已拒绝
      createdAt: new Date(),
      updatedAt: new Date()
    }
    
    const result = await db.collection('coaches').add({
      data: coachData
    })
    
    console.log('教练注册成功：', result._id)
    
    return {
      success: true,
      message: '注册申请提交成功，请等待审核',
      coachId: result._id
    }
  } catch (error) {
    console.error('教练注册失败：', error)
    return {
      success: false,
      error: error.message || '注册失败，请重试'
    }
  }
}
