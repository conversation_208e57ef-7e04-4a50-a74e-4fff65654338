/**index.wxss**/

/* 首页样式 - 分层筛选设计 */
page {
  background: #f8fafc;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
}

.container {
  min-height: 100vh;
  padding-top: 40rpx;
  padding-bottom: 140rpx;
}

/* 页面标题 */
.page-title {
  text-align: center;
  padding: 40rpx 32rpx 32rpx 32rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  margin: 0 24rpx 32rpx 24rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

.title-text {
  font-size: 36rpx;
  font-weight: 700;
  color: #fff;
  text-shadow: 0 2rpx 8rpx rgba(0,0,0,0.2);
}

/* 固定顶部导航 */
.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 32rpx 24rpx 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.app-title {
  text-align: center;
  color: #fff;
}

.title-text {
  font-size: 44rpx;
  font-weight: 700;
  display: block;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);
}

.title-desc {
  font-size: 24rpx;
  opacity: 0.9;
  display: block;
  font-weight: 300;
}

/* 主要内容区域 */
.main-content {
  padding: 32rpx 24rpx;
}

/* 搜索区域 */
.search-section {
  margin-bottom: 32rpx;
}

.search-box {
  position: relative;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
  overflow: hidden;
}

.search-input {
  width: 100%;
  height: 88rpx;
  padding: 0 60rpx 0 32rpx;
  font-size: 28rpx;
  color: #333;
  background: transparent;
}

.search-input::placeholder {
  color: #999;
}

.search-icon {
  position: absolute;
  right: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32rpx;
  color: #666;
}

/* 筛选区域 */
.filter-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
  margin-bottom: 32rpx;
}

.filter-row {
  display: flex;
  gap: 24rpx;
  margin-bottom: 24rpx;
}

.filter-row:last-of-type {
  margin-bottom: 32rpx;
}

.filter-item {
  flex: 1;
}

.filter-label {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.picker-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 72rpx;
  padding: 0 24rpx;
  background: #f8fafc;
  border-radius: 12rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.picker-display:active {
  border-color: #667eea;
  background: #f0f4ff;
}

.picker-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.picker-arrow {
  font-size: 20rpx;
  color: #999;
  margin-left: 12rpx;
}

/* 筛选操作区域 */
.filter-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 24rpx;
  border-top: 2rpx solid #f0f0f0;
}

.reset-btn {
  padding: 16rpx 32rpx;
  background: #f8fafc;
  color: #666;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: none;
  transition: all 0.3s ease;
}

.reset-btn:active {
  background: #e2e8f0;
}

.result-count {
  font-size: 24rpx;
  color: #667eea;
  font-weight: 500;
}

/* 结果展示区域 */
.results-section {
  margin-bottom: 32rpx;
}

/* 加载状态 */
.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 0;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 24rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 错误状态 */
.error-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 0;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
}

.error-text {
  font-size: 28rpx;
  color: #ff6b6b;
  margin-bottom: 24rpx;
}

.retry-btn {
  padding: 16rpx 32rpx;
  background: #667eea;
  color: #fff;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: none;
}

/* 空状态 */
.empty-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 0;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.empty-desc {
  font-size: 24rpx;
  color: #999;
}

/* 教练卡片 */
.coach-results {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.coach-card {
  display: flex;
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.coach-card:active {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 30rpx rgba(0,0,0,0.12);
}

.coach-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 16rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
  background: #f8fafc;
}

.coach-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.coach-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.coach-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.coach-rating {
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.rating-score {
  font-size: 28rpx;
  font-weight: 600;
  color: #ff9500;
}

.rating-star {
  font-size: 24rpx;
  color: #ff9500;
}

.rating-count {
  font-size: 20rpx;
  color: #999;
}

.coach-tags {
  display: flex;
  gap: 12rpx;
  margin-bottom: 12rpx;
  flex-wrap: wrap;
}

.tag {
  padding: 6rpx 16rpx;
  background: #f0f4ff;
  color: #667eea;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.coach-specialty {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 16rpx;
  line-height: 1.4;
}

.coach-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.coach-price {
  font-size: 28rpx;
  font-weight: 600;
  color: #ff6b6b;
}

.coach-gym {
  font-size: 20rpx;
  color: #999;
}

.coach-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 60rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border-radius: 30rpx;
  margin-left: 24rpx;
  flex-shrink: 0;
}

.action-text {
  font-size: 24rpx;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .filter-row {
    flex-direction: column;
    gap: 16rpx;
  }

  .coach-card {
    padding: 24rpx;
  }

  .coach-avatar {
    width: 100rpx;
    height: 100rpx;
  }
}
/* 固定底部导航 */


/* 第二层：城市选择 */
.secondary-layer {
  border-left: 6rpx solid #f59e0b;
}

.city-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}

.city-option {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 16rpx;
  border: 2rpx solid #e2e8f0;
  border-radius: 12rpx;
  background: #f8fafc;
  transition: all 0.3s ease;
  position: relative;
  min-height: 80rpx;
}

.city-option.selected {
  border-color: #f59e0b;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: #fff;
  transform: scale(1.05);
  box-shadow: 0 6rpx 20rpx rgba(245, 158, 11, 0.3);
}

.city-name {
  font-size: 26rpx;
  font-weight: 600;
  text-align: center;
}

.city-check {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #fff;
  color: #f59e0b;
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16rpx;
  font-weight: 700;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

/* 第三层：价格选择 */
.tertiary-layer {
  border-left: 6rpx solid #10b981;
}

.price-options {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.price-option {
  display: flex;
  align-items: center;
  padding: 20rpx 16rpx;
  border: 2rpx solid #e2e8f0;
  border-radius: 12rpx;
  background: #f8fafc;
  transition: all 0.3s ease;
  position: relative;
}

.price-option.selected {
  border-color: #10b981;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: #fff;
  transform: scale(1.02);
  box-shadow: 0 6rpx 20rpx rgba(16, 185, 129, 0.3);
}

.price-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.price-content {
  flex: 1;
}

.price-title {
  font-size: 28rpx;
  font-weight: 600;
  margin-bottom: 4rpx;
  display: block;
}

.price-desc {
  font-size: 22rpx;
  opacity: 0.8;
  display: block;
}

.price-check {
  font-size: 24rpx;
  color: #fff;
  font-weight: 700;
}

/* 第四层：时间偏好 */
.quaternary-layer {
  border-left: 6rpx solid #3b82f6;
}

.time-preferences {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.time-option {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 16rpx;
  border: 2rpx solid #e2e8f0;
  border-radius: 12rpx;
  background: #f8fafc;
  transition: all 0.3s ease;
  position: relative;
  min-height: 80rpx;
}

.time-option.selected {
  border-color: #3b82f6;
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: #fff;
  transform: scale(1.05);
  box-shadow: 0 6rpx 20rpx rgba(59, 130, 246, 0.3);
}

.time-label {
  font-size: 26rpx;
  font-weight: 600;
  text-align: center;
}

.time-check {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #fff;
  color: #3b82f6;
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16rpx;
  font-weight: 700;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

/* 第五层：教练风格 */
.style-layer {
  border-left: 6rpx solid #ec4899;
}

.style-options {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.style-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 16rpx;
  border: 2rpx solid #e2e8f0;
  border-radius: 12rpx;
  background: #f8fafc;
  transition: all 0.3s ease;
  position: relative;
  min-height: 120rpx;
}

.style-option.selected {
  border-color: #ec4899;
  background: linear-gradient(135deg, #ec4899 0%, #db2777 100%);
  color: #fff;
  transform: scale(1.05);
  box-shadow: 0 6rpx 20rpx rgba(236, 72, 153, 0.3);
}

.style-icon {
  font-size: 40rpx;
  margin-bottom: 12rpx;
}

.style-label {
  font-size: 24rpx;
  font-weight: 600;
  text-align: center;
}

.style-check {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #fff;
  color: #ec4899;
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16rpx;
  font-weight: 700;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

/* 第六层：课程类型 */
.course-layer {
  border-left: 6rpx solid #8b5cf6;
}

.course-options {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.course-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 16rpx;
  border: 2rpx solid #e2e8f0;
  border-radius: 12rpx;
  background: #f8fafc;
  transition: all 0.3s ease;
  position: relative;
  min-height: 120rpx;
}

.course-option.selected {
  border-color: #8b5cf6;
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: #fff;
  transform: scale(1.05);
  box-shadow: 0 6rpx 20rpx rgba(139, 92, 246, 0.3);
}

.course-icon {
  font-size: 40rpx;
  margin-bottom: 12rpx;
}

.course-label {
  font-size: 24rpx;
  font-weight: 600;
  text-align: center;
}

.course-check {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #fff;
  color: #8b5cf6;
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16rpx;
  font-weight: 700;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

/* 教练结果展示区域 */
.results-section {
  margin: 0 24rpx 24rpx 24rpx;
  background: #fff;
  border-radius: 20rpx;
  padding: 32rpx 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.06);
  border-left: 6rpx solid #22c55e;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.results-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #1e293b;
}

.reset-all-btn {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  color: #64748b;
  border: 1rpx solid #cbd5e1;
  border-radius: 20rpx;
  padding: 12rpx 16rpx;
  font-size: 22rpx;
  display: flex;
  align-items: center;
  gap: 6rpx;
  transition: all 0.2s;
}

.reset-all-btn:active {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  transform: scale(0.95);
}

.reset-icon {
  font-size: 18rpx;
}

.coach-results {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

/* 教练结果卡片 */
.result-coach-card {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border: 1rpx solid #e2e8f0;
  border-radius: 16rpx;
  background: #f8fafc;
  transition: all 0.3s ease;
}

.result-coach-card:active {
  transform: scale(0.98);
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.result-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 16rpx;
  border: 2rpx solid #e2e8f0;
}

.result-info {
  flex: 1;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.result-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #1e293b;
}

.result-rating {
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.rating-score {
  font-size: 24rpx;
  font-weight: 600;
  color: #f59e0b;
}

.rating-star {
  font-size: 20rpx;
  color: #f59e0b;
}

.result-specialty {
  font-size: 22rpx;
  color: #64748b;
  margin-bottom: 8rpx;
}

.result-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-price {
  font-size: 26rpx;
  font-weight: 600;
  color: #dc2626;
}

.result-location {
  font-size: 20rpx;
  color: #64748b;
}

.result-action {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  margin-left: 16rpx;
}

.action-text {
  font-size: 22rpx;
  font-weight: 600;
}

/* 特定筛选器样式 */
.filter-item.climbing-type {
  min-width: 160rpx;
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
  border-color: #a7f3d0;
}

.filter-item.climbing-type.selected {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.filter-item.city {
  min-width: 120rpx;
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border-color: #fcd34d;
}

.filter-item.city.selected {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.filter-item.level {
  min-width: 100rpx;
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border-color: #93c5fd;
}

.filter-item.level.selected {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

.filter-item.specialty {
  min-width: 120rpx;
  background: linear-gradient(135deg, #fce7f3 0%, #fbcfe8 100%);
  border-color: #f9a8d4;
}

.filter-item.specialty.selected {
  background: linear-gradient(135deg, #ec4899 0%, #db2777 100%);
}

.filter-item.price {
  min-width: 140rpx;
  background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);
  border-color: #c4b5fd;
}

.filter-item.price.selected {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

/* 结果统计区域 */
.results-section {
  margin: 0 32rpx 24rpx 32rpx;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 24rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.04);
}

.results-info {
  display: flex;
  align-items: baseline;
  gap: 8rpx;
}

.results-count {
  font-size: 48rpx;
  font-weight: 700;
  color: #667eea;
  line-height: 1;
}

.results-label {
  font-size: 28rpx;
  color: #64748b;
  font-weight: 500;
}

.clear-filters {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  color: #64748b;
  border: 1rpx solid #cbd5e1;
  border-radius: 20rpx;
  padding: 12rpx 16rpx;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  gap: 6rpx;
  transition: all 0.2s;
}

.clear-filters:active {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  transform: scale(0.95);
}

.clear-icon {
  font-size: 20rpx;
  line-height: 1;
}

.clear-text {
  font-size: 22rpx;
}
/* 教练列表区域 */
.coaches-section {
  margin: 0 32rpx;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 80rpx 0;
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.06);
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f1f5f9;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 24rpx auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #64748b;
}

/* 错误状态 */
.error-state {
  text-align: center;
  padding: 80rpx 0;
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.06);
}

.error-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  display: block;
}

.error-text {
  font-size: 28rpx;
  color: #64748b;
  margin-bottom: 32rpx;
  display: block;
}

.retry-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border: none;
  border-radius: 24rpx;
  padding: 16rpx 32rpx;
  font-size: 26rpx;
}

/* 教练列表 */
.coach-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

/* 教练卡片 */
.coach-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 0;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.coach-card:active {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.12);
}

.card-header {
  position: relative;
  padding: 24rpx 24rpx 0 24rpx;
}

.coach-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid #fff;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
}

.coach-badge {
  position: absolute;
  top: 16rpx;
  right: 24rpx;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: 600;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
}

.coach-info {
  padding: 20rpx 24rpx 24rpx 24rpx;
}

.coach-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.coach-name {
  font-size: 36rpx;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 8rpx;
}

.rating-section {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.rating-score {
  font-size: 32rpx;
  font-weight: 700;
  color: #f59e0b;
}

.rating-stars {
  font-size: 24rpx;
  color: #f59e0b;
  letter-spacing: 2rpx;
}

.rating-count {
  font-size: 22rpx;
  color: #94a3b8;
}
/* 教练标签 */
.coach-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.level-tag {
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 600;
  border: 2rpx solid;
}

.level-tag.boulder {
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
  color: #059669;
  border-color: #a7f3d0;
}

.level-tag.lead {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  color: #2563eb;
  border-color: #93c5fd;
}

.location-tag {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  color: #d97706;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;
}

/* 教练专长 */
.coach-specialty {
  margin-bottom: 20rpx;
  padding: 16rpx;
  background: #f8fafc;
  border-radius: 12rpx;
  border-left: 4rpx solid #667eea;
}

.specialty-label {
  font-size: 24rpx;
  color: #64748b;
  font-weight: 500;
}

.specialty-text {
  font-size: 26rpx;
  color: #334155;
  line-height: 1.4;
}

/* 教练卡片底部 */
.coach-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16rpx;
  border-top: 1rpx solid #f1f5f9;
}

.price-section {
  flex: 1;
}

.price-label {
  font-size: 24rpx;
  color: #64748b;
  margin-bottom: 4rpx;
  display: block;
}

.price-info {
  display: flex;
  align-items: baseline;
  gap: 4rpx;
}

.price-amount {
  font-size: 36rpx;
  font-weight: 700;
  color: #dc2626;
}

.price-unit {
  font-size: 24rpx;
  color: #64748b;
}

.action-section {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.deposit-tag {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  color: #d97706;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.book-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
  transition: all 0.2s;
}

.book-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
}

.book-text {
  font-size: 24rpx;
  font-weight: 600;
}
/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 0;
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.06);
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 24rpx;
  display: block;
  opacity: 0.6;
}

.empty-title {
  font-size: 32rpx;
  color: #64748b;
  font-weight: 600;
  margin-bottom: 12rpx;
  display: block;
}

.empty-subtitle {
  font-size: 26rpx;
  color: #94a3b8;
  margin-bottom: 32rpx;
  display: block;
  line-height: 1.4;
}

.reset-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border: none;
  border-radius: 24rpx;
  padding: 16rpx 32rpx;
  font-size: 26rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}
/* 固定底部导航 */
.fixed-bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255,255,255,0.95);
  backdrop-filter: blur(20rpx);
  padding: 16rpx 24rpx;
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
  display: flex;
  gap: 16rpx;
  box-shadow: 0 -8rpx 32rpx rgba(0,0,0,0.1);
  border-top: 1rpx solid rgba(0,0,0,0.05);
}

.nav-btn {
  flex: 1;
  height: 100rpx;
  border-radius: 24rpx;
  border: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  transition: all 0.3s ease;
  box-shadow: 0 6rpx 20rpx rgba(0,0,0,0.1);
  position: relative;
  overflow: hidden;
}

.nav-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: left 0.5s;
}

.nav-btn:active::before {
  left: 100%;
}

.nav-btn:active {
  transform: scale(0.96);
}

.appointments-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
}

.appointments-btn:active {
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.4);
}

.coach-btn {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: #fff;
}

.test-btn {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: #fff;
}

.coach-btn:active {
  box-shadow: 0 4rpx 16rpx rgba(255, 107, 107, 0.4);
}

.nav-icon {
  font-size: 32rpx;
  line-height: 1;
}

.nav-text {
  font-size: 24rpx;
  line-height: 1;
  font-weight: 600;
}
