<view class="success-container">
  <view class="success-icon">
    <text class="icon">✓</text>
  </view>

  <view class="success-title">联系申请已提交</view>
  <view class="success-subtitle">教练将尽快联系您确认</view>

  <view class="appointment-info">
    <view class="info-item">
      <text class="label">申请编号：</text>
      <text class="value">{{appointmentId}}</text>
    </view>

    <view class="deposit-info" wx:if="{{needDeposit}}">
      <view class="deposit-title">💰 课程费用</view>
      <view class="deposit-amount">参考费用：¥{{depositAmount}}</view>
      <view class="deposit-tip">请添加教练微信详细沟通</view>

      <view class="wechat-info">
        <text class="wechat-label">教练微信：</text>
        <text class="wechat-id">{{coachWechat}}</text>
        <button class="copy-btn" bindtap="copyWechat">复制</button>
      </view>
    </view>

    <view class="no-deposit-info" wx:else>
      <view class="no-deposit-title">✅ 直接联系</view>
      <view class="no-deposit-tip">请等待教练回复确认</view>
    </view>
  </view>

  <view class="next-steps">
    <text class="steps-title">接下来：</text>
    <text class="step-item">1. 教练将通过微信联系您确认时间</text>
    <text class="step-item" wx:if="{{needDeposit}}">2. 请按约定安排课程费用</text>
    <text class="step-item">{{needDeposit ? '3' : '2'}}. 按时到达上课地点</text>
  </view>

  <view class="actions">
    <button class="btn secondary" bindtap="goToMyAppointments">查看我的记录</button>
    <button class="btn primary" bindtap="goToHome">返回首页</button>
  </view>
</view>