Page({
  data: {
    currentYear: 2024,
    currentMonth: 1,
    calendarDays: [],
    selectedDate: '',
    selectedDateText: '',
    daySchedules: [],
    showTimeModal: false,
    showWeeklyModal: false,
    editingSlot: null,
    timeForm: {
      startTime: '',
      endTime: '',
      repeat: 'none'
    },
    canSave: false,
    // 每周模板相关
    weekdayOptions: [
      { value: 1, label: '周一', checked: false },
      { value: 2, label: '周二', checked: false },
      { value: 3, label: '周三', checked: false },
      { value: 4, label: '周四', checked: false },
      { value: 5, label: '周五', checked: false },
      { value: 6, label: '周六', checked: false },
      { value: 0, label: '周日', checked: false }
    ],
    weeklyTimeSlots: [{ startTime: '', endTime: '' }],
    weeklyStartDate: '',
    weeklyEndDate: '',
    canApplyWeekly: false
  },

  onLoad() {
    const now = new Date();
    this.setData({
      currentYear: now.getFullYear(),
      currentMonth: now.getMonth() + 1
    });
    this.generateCalendar();
    this.loadScheduleData();
  },

  generateCalendar() {
    const { currentYear, currentMonth } = this.data;
    const firstDay = new Date(currentYear, currentMonth - 1, 1);
    const lastDay = new Date(currentYear, currentMonth, 0);
    const firstDayWeek = firstDay.getDay();
    const daysInMonth = lastDay.getDate();
    
    const calendarDays = [];
    const today = new Date();
    const todayStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
    
    // 添加上个月的日期
    const prevMonth = currentMonth === 1 ? 12 : currentMonth - 1;
    const prevYear = currentMonth === 1 ? currentYear - 1 : currentYear;
    const prevMonthLastDay = new Date(prevYear, prevMonth, 0).getDate();
    
    for (let i = firstDayWeek - 1; i >= 0; i--) {
      const day = prevMonthLastDay - i;
      const dateStr = `${prevYear}-${String(prevMonth).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      calendarDays.push({
        day: day,
        date: dateStr,
        isCurrentMonth: false,
        isToday: false,
        hasSchedule: false,
        scheduleCount: 0
      });
    }
    
    // 添加当月的日期
    for (let day = 1; day <= daysInMonth; day++) {
      const dateStr = `${currentYear}-${String(currentMonth).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      calendarDays.push({
        day: day,
        date: dateStr,
        isCurrentMonth: true,
        isToday: dateStr === todayStr,
        hasSchedule: false,
        scheduleCount: 0
      });
    }
    
    // 添加下个月的日期
    const nextMonth = currentMonth === 12 ? 1 : currentMonth + 1;
    const nextYear = currentMonth === 12 ? currentYear + 1 : currentYear;
    const remainingDays = 42 - calendarDays.length;
    
    for (let day = 1; day <= remainingDays; day++) {
      const dateStr = `${nextYear}-${String(nextMonth).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      calendarDays.push({
        day: day,
        date: dateStr,
        isCurrentMonth: false,
        isToday: false,
        hasSchedule: false,
        scheduleCount: 0
      });
    }
    
    this.setData({ calendarDays });
  },

  async loadScheduleData() {
    try {
      const res = await wx.cloud.callFunction({
        name: 'getCoachSchedule',
        data: {
          year: this.data.currentYear,
          month: this.data.currentMonth
        }
      });
      
      if (res.result.success) {
        this.updateCalendarWithSchedule(res.result.data || []);
      }
    } catch (error) {
      console.error('加载时间安排失败：', error);
    }
  },

  updateCalendarWithSchedule(scheduleData) {
    const { calendarDays } = this.data;
    const scheduleMap = {};
    
    // 统计每天的时间段数量
    scheduleData.forEach(item => {
      const date = item.date;
      if (!scheduleMap[date]) {
        scheduleMap[date] = 0;
      }
      scheduleMap[date]++;
    });
    
    // 更新日历显示
    const updatedDays = calendarDays.map(day => ({
      ...day,
      hasSchedule: !!scheduleMap[day.date],
      scheduleCount: scheduleMap[day.date] || 0
    }));
    
    this.setData({ calendarDays: updatedDays });
  },

  prevMonth() {
    let { currentYear, currentMonth } = this.data;
    currentMonth--;
    if (currentMonth < 1) {
      currentMonth = 12;
      currentYear--;
    }
    this.setData({ currentYear, currentMonth });
    this.generateCalendar();
    this.loadScheduleData();
  },

  nextMonth() {
    let { currentYear, currentMonth } = this.data;
    currentMonth++;
    if (currentMonth > 12) {
      currentMonth = 1;
      currentYear++;
    }
    this.setData({ currentYear, currentMonth });
    this.generateCalendar();
    this.loadScheduleData();
  },

  selectDate(e) {
    const { date, isCurrentMonth } = e.currentTarget.dataset;
    if (!isCurrentMonth) return;
    
    const dateObj = new Date(date);
    const selectedDateText = `${dateObj.getMonth() + 1}月${dateObj.getDate()}日`;
    
    this.setData({
      selectedDate: date,
      selectedDateText: selectedDateText
    });
    
    this.loadDaySchedule(date);
  },

  async loadDaySchedule(date) {
    try {
      const res = await wx.cloud.callFunction({
        name: 'getCoachDaySchedule',
        data: { date }
      });
      
      if (res.result.success) {
        const schedules = this.processDaySchedules(res.result.data || []);
        this.setData({ daySchedules: schedules });
      }
    } catch (error) {
      console.error('加载当天时间安排失败：', error);
    }
  },

  processDaySchedules(schedules) {
    return schedules.map(item => {
      const statusMap = {
        'available': { text: '可预约', class: 'available' },
        'booked': { text: '已预约', class: 'booked' },
        'completed': { text: '已完成', class: 'completed' }
      };
      
      const statusInfo = statusMap[item.status] || { text: item.status, class: 'unknown' };
      
      return {
        ...item,
        statusText: statusInfo.text,
        statusClass: statusInfo.class
      };
    });
  },

  addTimeSlot() {
    this.setData({
      showTimeModal: true,
      editingSlot: null,
      timeForm: {
        startTime: '',
        endTime: '',
        repeat: 'none'
      },
      canSave: false
    });
  },

  editTimeSlot(e) {
    const id = e.currentTarget.dataset.id;
    const slot = this.data.daySchedules.find(s => s._id === id);
    
    if (slot) {
      this.setData({
        showTimeModal: true,
        editingSlot: slot,
        timeForm: {
          startTime: slot.startTime,
          endTime: slot.endTime,
          repeat: 'none'
        }
      });
      this.checkCanSave();
    }
  },

  closeTimeModal() {
    this.setData({ showTimeModal: false });
  },

  stopPropagation() {
    // 阻止事件冒泡，防止点击弹窗内容时关闭弹窗
  },

  onStartTimeChange(e) {
    this.setData({
      'timeForm.startTime': e.detail.value
    });
    this.checkCanSave();
  },

  onEndTimeChange(e) {
    this.setData({
      'timeForm.endTime': e.detail.value
    });
    this.checkCanSave();
  },

  onRepeatChange(e) {
    const value = e.currentTarget.dataset.value;
    this.setData({
      'timeForm.repeat': value
    });
  },

  checkCanSave() {
    const { startTime, endTime } = this.data.timeForm;
    const canSave = startTime && endTime && startTime < endTime;
    this.setData({ canSave });
  },

  async saveTimeSlot() {
    const { selectedDate, timeForm, editingSlot } = this.data;
    
    if (!this.data.canSave) return;
    
    wx.showLoading({ title: '保存中...' });
    
    try {
      const res = await wx.cloud.callFunction({
        name: editingSlot ? 'updateTimeSlot' : 'addTimeSlot',
        data: {
          slotId: editingSlot ? editingSlot._id : undefined,
          date: selectedDate,
          startTime: timeForm.startTime,
          endTime: timeForm.endTime,
          repeat: timeForm.repeat
        }
      });
      
      if (res.result.success) {
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });
        
        this.closeTimeModal();
        this.loadDaySchedule(selectedDate);
        this.loadScheduleData();
      } else {
        wx.showToast({
          title: res.result.error || '保存失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('保存时间段失败：', error);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  deleteTimeSlot(e) {
    const id = e.currentTarget.dataset.id;
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个时间段吗？',
      success: async (res) => {
        if (res.confirm) {
          await this.performDeleteTimeSlot(id);
        }
      }
    });
  },

  async performDeleteTimeSlot(slotId) {
    wx.showLoading({ title: '删除中...' });
    
    try {
      const res = await wx.cloud.callFunction({
        name: 'deleteTimeSlot',
        data: { slotId }
      });
      
      if (res.result.success) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });
        
        this.loadDaySchedule(this.data.selectedDate);
        this.loadScheduleData();
      } else {
        wx.showToast({
          title: res.result.error || '删除失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('删除时间段失败：', error);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  viewAppointment(e) {
    const appointmentId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/coachAppointmentDetail/index?id=${appointmentId}`
    });
  },

  // 每周模板相关方法
  showWeeklyTemplate() {
    // 设置默认日期范围（接下来4周）
    const today = new Date();
    const startDate = today.toISOString().split('T')[0];
    const endDate = new Date(today.getTime() + 28 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

    this.setData({
      showWeeklyModal: true,
      weeklyStartDate: startDate,
      weeklyEndDate: endDate
    });
    this.checkCanApplyWeekly();
  },

  closeWeeklyModal() {
    this.setData({ showWeeklyModal: false });
  },

  toggleWeekday(e) {
    const value = e.currentTarget.dataset.value;
    const weekdayOptions = this.data.weekdayOptions.map(item => ({
      ...item,
      checked: item.value === value ? !item.checked : item.checked
    }));

    this.setData({ weekdayOptions });
    this.checkCanApplyWeekly();
  },

  addTimeTemplate() {
    const weeklyTimeSlots = [...this.data.weeklyTimeSlots, { startTime: '', endTime: '' }];
    this.setData({ weeklyTimeSlots });
  },

  removeTimeTemplate(e) {
    const index = e.currentTarget.dataset.index;
    const weeklyTimeSlots = this.data.weeklyTimeSlots.filter((_, i) => i !== index);
    this.setData({ weeklyTimeSlots });
    this.checkCanApplyWeekly();
  },

  onTemplateStartTimeChange(e) {
    const index = e.currentTarget.dataset.index;
    const weeklyTimeSlots = [...this.data.weeklyTimeSlots];
    weeklyTimeSlots[index].startTime = e.detail.value;
    this.setData({ weeklyTimeSlots });
    this.checkCanApplyWeekly();
  },

  onTemplateEndTimeChange(e) {
    const index = e.currentTarget.dataset.index;
    const weeklyTimeSlots = [...this.data.weeklyTimeSlots];
    weeklyTimeSlots[index].endTime = e.detail.value;
    this.setData({ weeklyTimeSlots });
    this.checkCanApplyWeekly();
  },

  onWeeklyStartDateChange(e) {
    this.setData({ weeklyStartDate: e.detail.value });
    this.checkCanApplyWeekly();
  },

  onWeeklyEndDateChange(e) {
    this.setData({ weeklyEndDate: e.detail.value });
    this.checkCanApplyWeekly();
  },

  checkCanApplyWeekly() {
    const { weekdayOptions, weeklyTimeSlots, weeklyStartDate, weeklyEndDate } = this.data;

    const hasSelectedWeekday = weekdayOptions.some(item => item.checked);
    const hasValidTimeSlot = weeklyTimeSlots.some(slot =>
      slot.startTime && slot.endTime && slot.startTime < slot.endTime
    );
    const hasDateRange = weeklyStartDate && weeklyEndDate && weeklyStartDate <= weeklyEndDate;

    this.setData({
      canApplyWeekly: hasSelectedWeekday && hasValidTimeSlot && hasDateRange
    });
  },

  async applyWeeklyTemplate() {
    if (!this.data.canApplyWeekly) return;

    wx.showLoading({ title: '应用模板中...' });

    try {
      const res = await wx.cloud.callFunction({
        name: 'applyWeeklyTemplate',
        data: {
          weekdays: this.data.weekdayOptions.filter(item => item.checked).map(item => item.value),
          timeSlots: this.data.weeklyTimeSlots.filter(slot =>
            slot.startTime && slot.endTime && slot.startTime < slot.endTime
          ),
          startDate: this.data.weeklyStartDate,
          endDate: this.data.weeklyEndDate
        }
      });

      if (res.result.success) {
        wx.showToast({
          title: `成功添加${res.result.addedCount}个时间段`,
          icon: 'success'
        });

        this.closeWeeklyModal();
        this.loadScheduleData();
        if (this.data.selectedDate) {
          this.loadDaySchedule(this.data.selectedDate);
        }
      } else {
        wx.showToast({
          title: res.result.error || '应用失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('应用每周模板失败：', error);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  showBatchAdd() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  }
});
