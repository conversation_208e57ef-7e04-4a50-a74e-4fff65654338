const cloud = require('wx-server-sdk')
cloud.init()
const db = cloud.database()

exports.main = async (event, context) => {
  // 获取用户openid
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  try {
    // 获取教练信息
    const coachRes = await db.collection('coaches')
      .where({ openid: openid })
      .get()
    
    if (coachRes.data.length === 0) {
      return {
        success: false,
        error: '教练信息不存在'
      }
    }
    
    const coach = coachRes.data[0]
    const coachId = coach._id
    
    // 查询教练的所有课程记录
    const appointmentsRes = await db.collection('appointments')
      .where({
        coachId: coachId
      })
      .orderBy('createdAt', 'desc')
      .get()
    
    return {
      success: true,
      data: appointmentsRes.data
    }
  } catch (error) {
    console.error('获取教练预约列表失败：', error)
    return {
      success: false,
      error: error.message
    }
  }
}
