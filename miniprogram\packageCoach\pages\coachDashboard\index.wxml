<view class="dashboard-container">
  <view class="header">
    <view class="coach-info">
      <image class="coach-avatar" src="{{coachInfo.avatar}}" mode="aspectFill" />
      <view class="coach-details">
        <text class="coach-name">{{coachInfo.name}}</text>
        <text class="coach-gym">{{coachInfo.gym}}</text>
      </view>
    </view>
    <button class="logout-btn" bindtap="logout">退出</button>
  </view>
  
  <view class="stats-section">
    <view class="stats-title">今日数据</view>
    <view class="stats-grid">
      <view class="stat-item">
        <text class="stat-number">{{todayStats.appointments}}</text>
        <text class="stat-label">今日课程</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">¥{{todayStats.income}}</text>
        <text class="stat-label">今日收入</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{todayStats.newReviews}}</text>
        <text class="stat-label">新评价</text>
      </view>
    </view>
  </view>
  
  <view class="monthly-section">
    <view class="monthly-title">本月统计</view>
    <view class="monthly-grid">
      <view class="monthly-item">
        <text class="monthly-label">总课程</text>
        <text class="monthly-value">{{monthlyStats.totalAppointments}}次</text>
      </view>
      <view class="monthly-item">
        <text class="monthly-label">总收入</text>
        <text class="monthly-value">¥{{monthlyStats.totalIncome}}</text>
      </view>
      <view class="monthly-item">
        <text class="monthly-label">平均评分</text>
        <text class="monthly-value">{{monthlyStats.avgRating}}分</text>
      </view>
      <view class="monthly-item">
        <text class="monthly-label">完成率</text>
        <text class="monthly-value">{{monthlyStats.completionRate}}%</text>
      </view>
    </view>
  </view>
  
  <view class="quick-actions">
    <view class="actions-title">快速操作</view>
    <view class="actions-grid">
      <button class="action-btn" bindtap="goToAppointments">
        <text class="action-icon">📅</text>
        <text class="action-text">学员管理</text>
        <text class="action-badge" wx:if="{{pendingCount > 0}}">{{pendingCount}}</text>
      </button>
      
      <button class="action-btn" bindtap="goToSchedule">
        <text class="action-icon">🗓️</text>
        <text class="action-text">时间管理</text>
      </button>
      
      <button class="action-btn" bindtap="goToReviews">
        <text class="action-icon">⭐</text>
        <text class="action-text">评价管理</text>
        <text class="action-badge" wx:if="{{newReviewsCount > 0}}">{{newReviewsCount}}</text>
      </button>
      
      <button class="action-btn" bindtap="goToProfile">
        <text class="action-icon">👤</text>
        <text class="action-text">个人主页</text>
      </button>
      
      <button class="action-btn" bindtap="goToCourses">
        <text class="action-icon">📚</text>
        <text class="action-text">课程管理</text>
      </button>
      
      <button class="action-btn" bindtap="goToEarnings">
        <text class="action-icon">💰</text>
        <text class="action-text">收入统计</text>
      </button>

      <button class="action-btn" bindtap="initCoachCollections">
        <text class="action-icon">🔧</text>
        <text class="action-text">初始化数据库</text>
      </button>
    </view>
  </view>
  
  <view class="recent-section">
    <view class="recent-title">最近课程</view>
    <view wx:if="{{recentAppointments.length === 0}}" class="empty-tip">
      暂无最近课程
    </view>
    <view wx:else class="recent-list">
      <block wx:for="{{recentAppointments}}" wx:key="_id">
        <view class="recent-item" bindtap="goToAppointmentDetail" data-id="{{item._id}}">
          <view class="recent-info">
            <text class="recent-student">{{item.studentName}}</text>
            <text class="recent-time">{{item.timeSlot.date}} {{item.timeSlot.start}}-{{item.timeSlot.end}}</text>
          </view>
          <text class="recent-status status-{{item.statusClass}}">{{item.statusText}}</text>
        </view>
      </block>
    </view>
  </view>
</view>
