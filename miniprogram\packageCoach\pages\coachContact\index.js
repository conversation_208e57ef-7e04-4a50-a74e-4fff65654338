// 教练联系方式页面
Page({
  data: {
    coachId: '',
    coach: {},
    contacts: {},
    loading: true,
    errorMsg: ''
  },

  onLoad(options) {
    this.setData({ coachId: options.coachId });
    this.loadCoachContacts();
  },

  async loadCoachContacts() {
    this.setData({ loading: true, errorMsg: '' });

    try {
      const res = await wx.cloud.callFunction({
        name: 'getCoachDetails',
        data: { coachId: this.data.coachId }
      });

      if (res.result.success) {
        const coach = res.result.data.coach;
        const contacts = coach.contacts || {};
        
        this.setData({
          coach: coach,
          contacts: contacts,
          loading: false
        });
      } else {
        this.setData({
          loading: false,
          errorMsg: res.result.error || '获取联系方式失败'
        });
      }
    } catch (error) {
      console.error('获取教练联系方式失败：', error);
      this.setData({
        loading: false,
        errorMsg: '网络错误，请重试'
      });
    }
  },

  // 复制联系方式
  copyContact(e) {
    const { type, value } = e.currentTarget.dataset;
    
    if (!value) {
      wx.showToast({
        title: '该联系方式暂未提供',
        icon: 'none'
      });
      return;
    }

    wx.setClipboardData({
      data: value,
      success: () => {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success'
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        });
      }
    });
  },

  // 拨打电话
  makeCall(e) {
    const phone = e.currentTarget.dataset.phone;
    
    if (!phone) {
      wx.showToast({
        title: '电话号码暂未提供',
        icon: 'none'
      });
      return;
    }

    wx.makePhoneCall({
      phoneNumber: phone,
      fail: () => {
        wx.showToast({
          title: '拨号失败',
          icon: 'none'
        });
      }
    });
  },

  // 返回上一页
  goBack() {
    wx.navigateBack();
  }
});
