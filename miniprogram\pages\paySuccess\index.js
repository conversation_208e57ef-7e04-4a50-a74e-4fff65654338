Page({
  data: {
    appointmentId: '',
    needDeposit: false,
    depositAmount: 0,
    coachWechat: ''
  },

  onLoad(options) {
    this.setData({
      appointmentId: options.appointmentId || '',
      needDeposit: options.needDeposit === 'true',
      depositAmount: parseInt(options.depositAmount) || 0,
      coachWechat: options.coachWechat || ''
    });
  },

  copyWechat() {
    wx.setClipboardData({
      data: this.data.coachWechat,
      success: () => {
        wx.showToast({
          title: '微信号已复制',
          icon: 'success'
        });
      }
    });
  },

  goToMyAppointments() {
    wx.redirectTo({
      url: '/pages/myAppointments/index'
    });
  },

  goToHome() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  }
});