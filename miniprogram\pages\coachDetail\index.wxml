<view class="coach-detail-container">
  <view class="coach-header">
    <button class="back-btn" bindtap="onBack">返回</button>
    <button class="share-btn" open-type="share">分享</button>
  </view>

  <image class="cover-img" src="{{coach.coverImg}}" mode="aspectFill" />

  <view class="coach-info-block">
    <image class="avatar" src="{{coach.avatar}}" mode="aspectFill" binderror="onAvatarError" />
    <view class="name-rating">
      <text class="name">{{coach.name}}</text>
      <text class="rating">{{coach.rating}}★ ({{coach.reviewCount}}条评价)</text>
    </view>

    <view class="ability-tags">
      <text class="section-subtitle">能力标签</text>
      <view class="tags">
        <text class="tag" wx:for="{{coach.boulderingLevelTags}}" wx:key="*this">抱石: {{item}}</text>
        <text class="tag" wx:for="{{coach.leadLevelTags}}" wx:key="*this">先锋: {{item}}</text>
        <text class="tag" wx:for="{{coach.certifications}}" wx:key="*this">认证: {{item}}</text>
      </view>
    </view>

    <view class="info-item">
      <text class="info-label">教学专长：</text>
      <text class="info-value">{{coach.specialty ? coach.specialty.join('、') : '攀岩教学'}}</text>
    </view>
    <view class="info-item">
      <text class="info-label">常驻地点：</text>
      <text class="info-value">{{coach.gym}}</text>
    </view>
    <view class="info-item">
      <text class="info-label">教学风格：</text>
      <text class="info-value">{{coach.style}}</text>
    </view>
  </view>

  <view class="deposit-block">
    <text class="section-title">⚠️ 定金规则</text>
    <view class="deposit-item">
      <text class="deposit-icon">💰</text>
      <text>需付定金：¥{{depositInfo.amount}} (课程费{{depositInfo.rate}}%)</text>
    </view>
    <view class="deposit-item">
      <text class="deposit-icon">📋</text>
      <text>取消政策：{{depositInfo.policy}}</text>
    </view>
    <view class="deposit-item">
      <text class="deposit-icon">💳</text>
      <text>支付方式：添加教练微信转账</text>
    </view>
  </view>

  <view class="review-block">
    <text class="section-title">学员评价</text>

    <!-- 评价标签统计 -->
    <view class="review-tags-summary" wx:if="{{reviewTagsStats.length > 0}}">
      <text class="tags-title">学员评价标签</text>
      <view class="tags-list">
        <block wx:for="{{reviewTagsStats}}" wx:key="tagId">
          <view class="tag-stat" style="background: {{item.color}}20; border-color: {{item.color}}">
            <text class="tag-icon">{{item.icon}}</text>
            <text class="tag-text" style="color: {{item.color}}">{{item.text}}</text>
            <text class="tag-count">({{item.count}})</text>
          </view>
        </block>
      </view>
    </view>

    <!-- 最近评价 -->
    <view class="recent-reviews" wx:if="{{reviews.length > 0}}">
      <text class="recent-title">最近评价</text>
      <block wx:for="{{reviews}}" wx:key="_id">
        <view class="review-item">
          <view class="review-header">
            <text class="review-rating">{{item.rating}}★</text>
            <text class="review-user">{{item.anonymous ? '匿名用户' : item.studentName}}</text>
            <text class="review-time">{{item.timeText}}</text>
          </view>
          <view class="review-tags" wx:if="{{item.tagTexts.length > 0}}">
            <block wx:for="{{item.tagTexts}}" wx:key="*this" wx:for-item="tag">
              <text class="review-tag">{{tag.icon}} {{tag.text}}</text>
            </block>
          </view>
        </view>
      </block>
    </view>

    <button class="all-reviews-btn" wx:if="{{coach.reviewCount > 3}}" bindtap="onAllReviews">
      查看全部{{coach.reviewCount}}条评价
    </button>

    <view class="no-reviews" wx:if="{{reviews.length === 0}}">
      <text class="no-reviews-text">暂无评价</text>
    </view>
  </view>

  <view class="course-block">
    <text class="section-title">可预约课程</text>
    <block wx:for="{{courses}}" wx:key="_id">
      <view class="course-item">
        <view class="course-header">
          <text class="course-title">[{{item.title}}]</text>
          <text class="course-duration">{{item.duration}}分钟</text>
          <text class="course-price">¥{{item.price}}</text>
        </view>
        <text class="course-intro">{{item.intro}}</text>
        <view class="course-actions">
          <button class="book-btn" bindtap="onBook" data-course-id="{{item._id}}">查看可约时间</button>
        </view>
      </view>
    </block>
  </view>

  <view class="bottom-actions">
    <button class="schedule-btn" bindtap="goToAppointment">
      <text class="btn-icon">📅</text>
      <text class="btn-text">查看可约时间</text>
    </button>
    <button class="contact-btn" bindtap="onConsultation">
      <text class="btn-icon">💬</text>
      <text class="btn-text">课前咨询</text>
    </button>
    <button class="appointment-btn" bindtap="goToAppointment">
      <text class="btn-icon">⚡</text>
      <text class="btn-text">联系教练</text>
    </button>
  </view>
</view>