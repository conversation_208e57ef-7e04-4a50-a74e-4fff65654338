/* 教练预约管理页样式 */
.appointments-container {
  background: #f6f6f6;
  min-height: 100vh;
}

.header {
  background: #667eea;
  padding: 32rpx;
  text-align: center;
}

.title {
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
}

.tabs {
  background: #fff;
  display: flex;
  border-bottom: 1rpx solid #f0f0f0;
}

.tab-btn {
  flex: 1;
  background: none;
  border: none;
  padding: 24rpx 0;
  font-size: 26rpx;
  color: #666;
  position: relative;
}

.tab-btn.active {
  color: #667eea;
  font-weight: 600;
}

.tab-btn.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: #667eea;
  border-radius: 2rpx;
}

.appointments-list {
  padding: 16rpx;
}

.loading, .error-msg {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding: 80rpx 0;
}

.error-msg {
  color: #e54545;
}

.empty-tip {
  text-align: center;
  padding: 80rpx 40rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.appointment-card {
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.student-info {
  flex: 1;
}

.student-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 4rpx;
}

.student-phone {
  font-size: 24rpx;
  color: #666;
  display: block;
}

.status-tag {
  padding: 6rpx 16rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.status-pending {
  background: #fff7e6;
  color: #fa8c16;
}

.status-confirmed {
  background: #e6f7ff;
  color: #1890ff;
}

.status-completed {
  background: #f6ffed;
  color: #52c41a;
}

.status-cancelled {
  background: #fff2f0;
  color: #ff4d4f;
}

.card-content {
  margin-bottom: 16rpx;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.label {
  font-size: 26rpx;
  color: #666;
  min-width: 80rpx;
}

.value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}

.card-actions {
  display: flex;
  gap: 16rpx;
  justify-content: flex-end;
}

.action-btn {
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  border: none;
}

.action-btn.primary {
  background: #667eea;
  color: #fff;
}

.action-btn.secondary {
  background: #f0f0f0;
  color: #333;
}
