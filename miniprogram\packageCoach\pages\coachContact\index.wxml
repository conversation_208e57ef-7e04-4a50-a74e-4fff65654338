<view class="contact-container">
  <!-- 页面标题 -->
  <view class="header">
    <button class="back-btn" bindtap="goBack">
      <text class="back-icon">←</text>
    </button>
    <text class="page-title">课前咨询</text>
    <view class="placeholder"></view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-section" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">获取联系方式中...</text>
  </view>

  <!-- 错误状态 -->
  <view class="error-section" wx:elif="{{errorMsg}}">
    <text class="error-text">{{errorMsg}}</text>
    <button class="retry-btn" bindtap="loadCoachContacts">重试</button>
  </view>

  <!-- 联系方式内容 -->
  <view class="content" wx:else>
    <!-- 教练信息 -->
    <view class="coach-info">
      <image class="coach-avatar" src="{{coach.avatar || 'cloud://miniprogram-15-7g0tuaph2c2c4e96.6d69-miniprogram-15-7g0tuaph2c2c4e96-1329434144/default-avatar.png'}}" mode="aspectFill" />
      <view class="coach-details">
        <text class="coach-name">{{coach.name}}</text>
        <text class="coach-gym">{{coach.gym || '攀岩教练'}}</text>
      </view>
    </view>

    <!-- 联系方式列表 -->
    <view class="contact-list">
      <view class="contact-title">联系方式</view>
      
      <!-- 微信 -->
      <view class="contact-item" wx:if="{{contacts.wechat}}">
        <view class="contact-icon wechat-icon">💬</view>
        <view class="contact-info">
          <text class="contact-label">微信号</text>
          <text class="contact-value">{{contacts.wechat}}</text>
        </view>
        <button class="copy-btn" bindtap="copyContact" data-type="wechat" data-value="{{contacts.wechat}}">
          复制
        </button>
      </view>

      <!-- 电话 -->
      <view class="contact-item" wx:if="{{contacts.contactPhone}}">
        <view class="contact-icon phone-icon">📞</view>
        <view class="contact-info">
          <text class="contact-label">联系电话</text>
          <text class="contact-value">{{contacts.contactPhone}}</text>
        </view>
        <button class="call-btn" bindtap="makeCall" data-phone="{{contacts.contactPhone}}">
          拨打
        </button>
      </view>

      <!-- 小红书 -->
      <view class="contact-item" wx:if="{{contacts.xiaohongshu}}">
        <view class="contact-icon xiaohongshu-icon">📖</view>
        <view class="contact-info">
          <text class="contact-label">小红书</text>
          <text class="contact-value">{{contacts.xiaohongshu}}</text>
        </view>
        <button class="copy-btn" bindtap="copyContact" data-type="xiaohongshu" data-value="{{contacts.xiaohongshu}}">
          复制
        </button>
      </view>

      <!-- 视频号 -->
      <view class="contact-item" wx:if="{{contacts.videoAccount}}">
        <view class="contact-icon video-icon">🎬</view>
        <view class="contact-info">
          <text class="contact-label">视频号</text>
          <text class="contact-value">{{contacts.videoAccount}}</text>
        </view>
        <button class="copy-btn" bindtap="copyContact" data-type="videoAccount" data-value="{{contacts.videoAccount}}">
          复制
        </button>
      </view>

      <!-- 无联系方式提示 -->
      <view class="no-contact" wx:if="{{!contacts.wechat && !contacts.contactPhone && !contacts.xiaohongshu && !contacts.videoAccount}}">
        <view class="no-contact-icon">📱</view>
        <text class="no-contact-text">该教练暂未提供联系方式</text>
        <text class="no-contact-desc">您可以通过其他方式联系教练</text>
      </view>
    </view>

    <!-- 温馨提示 -->
    <view class="tips-section">
      <view class="tips-title">💡 温馨提示</view>
      <view class="tips-content">
        <text class="tip-item">• 请通过以上方式联系教练进行课前沟通</text>
        <text class="tip-item">• 建议提前确认上课时间、地点和具体安排</text>
        <text class="tip-item">• 如有疑问可通过小程序反馈</text>
      </view>
    </view>
  </view>
</view>
