Page({
  data: {
    reviews: [],
    reviewStats: {
      avgRating: 0,
      totalReviews: 0,
      newReviews: 0
    },
    loading: false,
    errorMsg: ''
  },

  onLoad() {
    this.loadReviews();
  },

  onShow() {
    // 每次显示页面时刷新数据
    this.loadReviews();
  },

  async loadReviews() {
    this.setData({ loading: true, errorMsg: '' });
    
    try {
      const res = await wx.cloud.callFunction({
        name: 'getCoachReviews',
        data: {}
      });
      
      if (res.result.success) {
        const data = res.result.data;
        const processedReviews = this.processReviews(data.reviews || []);
        
        this.setData({
          reviews: processedReviews,
          reviewStats: data.stats || this.data.reviewStats,
          loading: false
        });
      } else {
        this.setData({
          loading: false,
          errorMsg: res.result.error || '加载失败'
        });
      }
    } catch (error) {
      console.error('获取评价列表失败：', error);
      this.setData({
        loading: false,
        errorMsg: '网络错误，请重试'
      });
    }
  },

  processReviews(reviews) {
    return reviews.map(item => {
      // 格式化时间
      const formatTime = (dateStr) => {
        if (!dateStr) return '';
        const date = new Date(dateStr);
        const now = new Date();
        const diff = now.getTime() - date.getTime();
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));
        
        if (days === 0) {
          return '今天';
        } else if (days === 1) {
          return '昨天';
        } else if (days < 7) {
          return `${days}天前`;
        } else {
          return `${date.getMonth() + 1}月${date.getDate()}日`;
        }
      };
      
      return {
        ...item,
        createdAtText: formatTime(item.createdAt),
        replyTime: item.replyTime ? formatTime(item.replyTime) : ''
      };
    });
  },

  previewImage(e) {
    const { url, urls } = e.currentTarget.dataset;
    wx.previewImage({
      current: url,
      urls: urls
    });
  },

  replyToReview(e) {
    const { id, student } = e.currentTarget.dataset;
    const review = this.data.reviews.find(r => r._id === id);
    
    if (review && review.reply) {
      // 已有回复，显示回复内容
      wx.showModal({
        title: '教练回复',
        content: review.reply,
        showCancel: false
      });
    } else {
      // 没有回复，跳转到回复页面
      wx.navigateTo({
        url: `/pages/replyReview/index?reviewId=${id}&studentName=${student}`
      });
    }
  }
});
