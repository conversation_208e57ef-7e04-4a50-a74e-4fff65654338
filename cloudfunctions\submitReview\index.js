const cloud = require('wx-server-sdk')
cloud.init()
const db = cloud.database()

exports.main = async (event, context) => {
  const {
    appointmentId,
    rating,
    anonymous = false,
    tags = [],
    feedback = ''
  } = event

  // 获取用户openid
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID

  if (!appointmentId || !rating) {
    return {
      success: false,
      error: '参数不完整：缺少预约ID或评分'
    }
  }

  if (rating < 1 || rating > 5) {
    return {
      success: false,
      error: '评分必须在1-5之间'
    }
  }

  if (!tags || tags.length === 0) {
    return {
      success: false,
      error: '请至少选择一个评价标签'
    }
  }

  try {
    // 获取预约信息
    const appointmentRes = await db.collection('appointments').doc(appointmentId).get()
    if (!appointmentRes.data) {
      return {
        success: false,
        error: '预约不存在'
      }
    }

    const appointment = appointmentRes.data

    // 验证预约状态和用户权限
    if (appointment.studentId !== openid) {
      return {
        success: false,
        error: '无权限评价此预约'
      }
    }

    if (appointment.status !== '已完成') {
      return {
        success: false,
        error: '只能评价已完成的课程'
      }
    }

    // 检查是否已经评价过
    const existingReview = await db.collection('reviews')
      .where({
        appointmentId: appointmentId,
        studentId: openid
      })
      .count()

    if (existingReview.total > 0) {
      return {
        success: false,
        error: '该课程已经评价过了'
      }
    }

    // 创建评价记录 - 新的标签式评价结构
    const reviewData = {
      appointmentId: appointmentId,
      coachId: appointment.coachId,
      studentId: openid,
      studentName: anonymous ? '匿名用户' : appointment.studentName,
      courseTitle: appointment.courseTitle,
      rating: parseInt(rating),
      tags: tags, // 标签ID数组
      anonymous: !!anonymous,
      timeSlot: appointment.timeSlot,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    const reviewRes = await db.collection('reviews').add({
      data: reviewData
    })

    // 更新预约状态为已评价
    await db.collection('appointments').doc(appointmentId).update({
      data: {
        hasReviewed: true,
        updatedAt: new Date()
      }
    })

    // 如果有反馈内容，也保存反馈
    if (feedback && feedback.trim()) {
      try {
        await db.collection('user_feedback').add({
          data: {
            openid: openid,
            type: 'review_tag_suggestion',
            content: feedback.trim(),
            relatedData: {
              reviewId: reviewRes._id,
              appointmentId: appointmentId,
              coachId: appointment.coachId
            },
            status: 'pending',
            createdAt: new Date(),
            updatedAt: new Date()
          }
        })
        console.log('用户反馈已保存')
      } catch (error) {
        console.error('保存用户反馈失败：', error)
        // 不影响评价提交的成功
      }
    }

    // 更新教练的评价统计（可选，这里简化处理）
    // 实际项目中可能需要重新计算教练的平均评分和评价数量

    return {
      success: true,
      data: {
        reviewId: reviewRes._id,
        message: '评价提交成功'
      }
    }
  } catch (error) {
    console.error('提交评价失败：', error)
    return {
      success: false,
      error: error.message
    }
  }
}