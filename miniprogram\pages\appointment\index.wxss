/* 课程预约页样式 */
.appointment-container {
  background: #f6f6f6;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.header {
  background: #fff;
  padding: 32rpx;
  margin-bottom: 16rpx;
}

.title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.coach-info {
  font-size: 26rpx;
  color: #666;
}

.time-section, .booking-info {
  background: #fff;
  margin-bottom: 16rpx;
  padding: 32rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 24rpx;
}

.loading, .error-msg {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding: 40rpx 0;
}

.error-msg {
  color: #e54545;
}

.date-group {
  margin-bottom: 32rpx;
}

.date-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 16rpx;
}

.slots-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.time-slot {
  background: #f0f0f0;
  border: 2rpx solid #f0f0f0;
  border-radius: 12rpx;
  padding: 16rpx 24rpx;
  font-size: 26rpx;
  color: #333;
  min-width: 160rpx;
  text-align: center;
}

.time-slot.selected {
  background: #e6f7ff;
  border-color: #007aff;
  color: #007aff;
}

.time-slot.booked {
  background: #f5f5f5;
  color: #ccc;
  border-color: #f5f5f5;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.label {
  font-size: 28rpx;
  color: #666;
  min-width: 120rpx;
}

.value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.form-section {
  margin-top: 24rpx;
}

.form-item {
  margin-bottom: 24rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.form-input {
  width: 100%;
  background: #f8f8f8;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 28rpx;
  border: 2rpx solid #f8f8f8;
}

.form-input:focus {
  border-color: #007aff;
  background: #fff;
}

.form-textarea {
  width: 100%;
  background: #f8f8f8;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 28rpx;
  border: 2rpx solid #f8f8f8;
  min-height: 120rpx;
}

.form-textarea:focus {
  border-color: #007aff;
  background: #fff;
}

.tips {
  background: #fffbe0;
  margin: 16rpx 32rpx;
  border-radius: 12rpx;
  padding: 24rpx;
  border-left: 4rpx solid #ffb400;
}

.tips-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #b8860b;
  display: block;
  margin-bottom: 12rpx;
}

.tips-content {
  font-size: 26rpx;
  color: #b8860b;
  line-height: 1.6;
  display: block;
  margin-bottom: 8rpx;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 16rpx 32rpx;
  box-shadow: 0 -2rpx 8rpx rgba(0,0,0,0.1);
}

.submit-btn {
  width: 100%;
  background: #007aff;
  color: #fff;
  border-radius: 24rpx;
  padding: 20rpx 0;
  font-size: 32rpx;
  border: none;
  font-weight: 600;
}

.submit-btn[disabled] {
  background: #ccc;
  color: #999;
}