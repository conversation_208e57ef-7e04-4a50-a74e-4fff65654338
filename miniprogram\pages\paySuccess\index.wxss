/* 支付成功页样式 */
.success-container {
  background: #f6f6f6;
  min-height: 100vh;
  padding: 60rpx 40rpx;
  text-align: center;
}

.success-icon {
  width: 120rpx;
  height: 120rpx;
  background: #52c41a;
  border-radius: 50%;
  margin: 0 auto 32rpx auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.success-icon .icon {
  font-size: 60rpx;
  color: #fff;
  font-weight: bold;
}

.success-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
}

.success-subtitle {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.appointment-info {
  background: #fff;
  border-radius: 12rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  text-align: left;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.label {
  font-size: 28rpx;
  color: #666;
  min-width: 140rpx;
}

.value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.deposit-info, .no-deposit-info {
  margin-top: 24rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid #f0f0f0;
}

.deposit-title, .no-deposit-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.deposit-amount {
  font-size: 32rpx;
  color: #e54545;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.deposit-tip, .no-deposit-tip {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.wechat-info {
  background: #f8f8f8;
  border-radius: 8rpx;
  padding: 16rpx;
  display: flex;
  align-items: center;
}

.wechat-label {
  font-size: 26rpx;
  color: #666;
  min-width: 120rpx;
}

.wechat-id {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  flex: 1;
}

.copy-btn {
  background: #007aff;
  color: #fff;
  border-radius: 16rpx;
  padding: 8rpx 20rpx;
  font-size: 24rpx;
  border: none;
}

.next-steps {
  background: #fff;
  border-radius: 12rpx;
  padding: 32rpx;
  margin-bottom: 40rpx;
  text-align: left;
}

.steps-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 16rpx;
}

.step-item {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  display: block;
  margin-bottom: 8rpx;
  padding-left: 16rpx;
}

.actions {
  display: flex;
  gap: 16rpx;
}

.btn {
  flex: 1;
  border-radius: 24rpx;
  padding: 20rpx 0;
  font-size: 28rpx;
  border: none;
}

.btn.primary {
  background: #007aff;
  color: #fff;
}

.btn.secondary {
  background: #f0f0f0;
  color: #333;
}