const { getTagById } = require('../../utils/reviewTags.js')

Page({
  data: {
    coach: {},
    courses: [],
    reviews: [],
    reviewTagsStats: [], // 评价标签统计
    depositInfo: {
      amount: 0,
      rate: 0,
      policy: ''
    },
    coachId: ''
  },
  onLoad(options) {
    this.setData({ coachId: options.id });
    this.loadCoachDetail(options.id);
  },
  async loadCoachDetail(coachId) {
    wx.showLoading({ title: '加载中' });
    try {
      const res = await wx.cloud.callFunction({
        name: 'getCoachFullInfo',
        data: { coachId: coachId }
      });

      console.log('获取教练详情结果：', res.result);

      if (res.result.success) {
        const { coach, recentReviews } = res.result.data;

        let depositInfo = { amount: 0, rate: 30, policy: '提前24小时以上取消可退定金；24小时内不退。' };
        if (coach.courses && coach.courses.length > 0) {
          const mainCourse = coach.courses[0];
          depositInfo = {
            amount: Math.round(mainCourse.price * 0.3),
            rate: 30,
            policy: '提前24小时以上取消可退定金；24小时内不退。'
          };
        }

        // 字段兼容处理
        if (coach) {
          coach.boulderingLevelTags = coach.boulderingLevel ? [coach.boulderingLevel] : [];
          coach.leadLevelTags = coach.leadLevel ? [coach.leadLevel] : [];
          coach.coverImg = (coach.gallery && coach.gallery.length > 0) ? coach.gallery[0] : 'cloud://miniprogram-15-7g0tuaph2c2c4e96.6d69-miniprogram-15-7g0tuaph2c2c4e96-1329434144/default-cover.png';
          coach.avatar = coach.avatar ? coach.avatar : 'cloud://miniprogram-15-7g0tuaph2c2c4e96.6d69-miniprogram-15-7g0tuaph2c2c4e96-1329434144/default-avatar.png';
          coach.specialty = Array.isArray(coach.specialty) ? coach.specialty : [coach.specialty || '攀岩教学'];
          coach.certifications = Array.isArray(coach.certifications) ? coach.certifications : [coach.certifications || 'CWA L1'];
          coach.rating = coach.rating ? coach.rating.toFixed(1) : '0.0';
        }

        // 模拟一些评价数据
        const mockReviews = [
          {
            _id: '1',
            rating: 5,
            anonymous: false,
            studentName: '李**',
            content: '教练帮我2周突破V3，动作分析超准！'
          },
          {
            _id: '2',
            rating: 4,
            anonymous: true,
            studentName: '匿名',
            content: '适合新手，但希望多演示动作'
          }
        ];

        // 处理评价数据，格式化标签显示
        const formattedReviews = this.formatReviews(recentReviews.length > 0 ? recentReviews : mockReviews);

        // 统计评价标签
        const tagStats = this.calculateTagStats(recentReviews);

        this.setData({
          coach: coach || {},
          courses: coach.courses || [],
          reviews: formattedReviews,
          reviewTagsStats: tagStats,
          depositInfo
        });
      } else {
        wx.showToast({ title: res.result.error || '加载失败', icon: 'none' });
      }
    } catch (e) {
      console.error('加载教练详情失败：', e);
      wx.showToast({ title: '网络错误，请重试', icon: 'none' });
    } finally {
      wx.hideLoading();
    }
  },
  onBook(e) {
    const courseId = e.currentTarget.dataset.courseId;
    const coachId = this.data.coach._id;

    // 跳转到预约页面，传递教练ID和课程ID
    wx.navigateTo({
      url: `/pages/appointment/index?coachId=${coachId}&courseId=${courseId}`
    });
  },
  onBack() {
    wx.navigateBack();
  },
  onAllReviews() {
    wx.navigateTo({
      url: `/pages/reviewList/index?coachId=${this.data.coach._id}`
    });
  },
  onConsultation() {
    wx.navigateTo({
      url: `/packageCoach/pages/coachContact/index?coachId=${this.data.coachId}`
    });
  },

  // 格式化评价数据，将标签ID转换为显示文本
  formatReviews(reviews) {
    return reviews.map(review => {
      const tagTexts = (review.tags || []).map(tagId => {
        const tagInfo = getTagById(tagId);
        return tagInfo ? { icon: tagInfo.icon, text: tagInfo.text } : null;
      }).filter(tag => tag !== null);

      return {
        ...review,
        tagTexts: tagTexts,
        timeText: this.formatTime(review.createdAt)
      };
    });
  },

  // 统计评价标签
  calculateTagStats(reviews) {
    const tagCounts = {};

    reviews.forEach(review => {
      if (review.tags && Array.isArray(review.tags)) {
        review.tags.forEach(tagId => {
          tagCounts[tagId] = (tagCounts[tagId] || 0) + 1;
        });
      }
    });

    // 转换为显示格式并排序
    const tagStats = Object.entries(tagCounts)
      .map(([tagId, count]) => {
        const tagInfo = getTagById(tagId);
        return tagInfo ? {
          tagId,
          text: tagInfo.text,
          icon: tagInfo.icon,
          color: tagInfo.categoryColor,
          count
        } : null;
      })
      .filter(tag => tag !== null)
      .sort((a, b) => b.count - a.count)
      .slice(0, 8); // 最多显示8个标签

    return tagStats;
  },

  // 格式化时间
  formatTime(timestamp) {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;

    if (diff < 24 * 60 * 60 * 1000) {
      return '今天';
    } else if (diff < 7 * 24 * 60 * 60 * 1000) {
      return `${Math.floor(diff / (24 * 60 * 60 * 1000))}天前`;
    } else {
      return `${date.getMonth() + 1}/${date.getDate()}`;
    }
  },

  goToAppointment() {
    wx.navigateTo({
      url: `/pages/appointment/index?coachId=${this.data.coachId}`
    });
  },

  onAvatarError() {
    // 头像加载失败时使用默认头像
    this.setData({
      'coach.avatar': 'cloud://miniprogram-15-7g0tuaph2c2c4e96.6d69-miniprogram-15-7g0tuaph2c2c4e96-1329434144/default-avatar.png'
    });
  }
});