Page({
  data: {
    coachId: '',
    courseId: 'default_course',
    coach: {},
    availableSlots: [],
    groupedSlots: [],
    selectedSlot: null,
    studentName: '',
    studentPhone: '',
    remark: '',
    loading: false,
    errorMsg: '',
    depositAmount: 0
  },

  onLoad(options) {
    this.setData({
      coachId: options.coachId || '',
      courseId: options.courseId || 'default_course'
    });
    this.loadCoachInfo();
    this.loadAvailableSlots();
  },

  async loadCoachInfo() {
    try {
      const res = await wx.cloud.callFunction({
        name: 'getCoachDetails',
        data: { coachId: this.data.coachId }
      });

      if (res.result.success) {
        const coach = res.result.data.coach;
        const depositAmount = coach.needDeposit ? Math.round(coach.price * 0.3) : 0;
        this.setData({
          coach: coach,
          depositAmount: depositAmount
        });
      }
    } catch (error) {
      console.error('获取教练信息失败：', error);
    }
  },

  async loadAvailableSlots() {
    this.setData({ loading: true, errorMsg: '' });

    try {
      const res = await wx.cloud.callFunction({
        name: 'getAvailableSlots',
        data: {
          coachId: this.data.coachId,
          courseId: this.data.courseId
        }
      });

      if (res.result.success) {
        const slots = res.result.data.slots;
        const groupedSlots = this.groupSlotsByDate(slots);
        this.setData({
          availableSlots: slots,
          groupedSlots: groupedSlots,
          loading: false
        });
      } else {
        this.setData({
          loading: false,
          errorMsg: res.result.error || '加载失败'
        });
      }
    } catch (error) {
      console.error('获取可选时间失败：', error);
      this.setData({
        loading: false,
        errorMsg: '网络错误，请重试'
      });
    }
  },

  groupSlotsByDate(slots) {
    const grouped = {};

    slots.forEach(slot => {
      if (slot.isBooked) return; // 跳过已占用的时段

      if (!grouped[slot.date]) {
        grouped[slot.date] = {
          date: slot.date,
          dateDisplay: this.formatDate(slot.date),
          slots: []
        };
      }
      grouped[slot.date].slots.push(slot);
    });

    return Object.values(grouped);
  },

  formatDate(dateStr) {
    const date = new Date(dateStr);
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);

    if (dateStr === today.toISOString().split('T')[0]) {
      return '今天';
    } else if (dateStr === tomorrow.toISOString().split('T')[0]) {
      return '明天';
    } else {
      const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
      const month = date.getMonth() + 1;
      const day = date.getDate();
      const weekday = weekdays[date.getDay()];
      return `${month}月${day}日 ${weekday}`;
    }
  },

  selectTimeSlot(e) {
    const slot = e.currentTarget.dataset.slot;
    this.setData({ selectedSlot: slot });
    this.checkCanSubmit();
  },

  onNameInput(e) {
    this.setData({ studentName: e.detail.value });
    this.checkCanSubmit();
  },

  onPhoneInput(e) {
    this.setData({ studentPhone: e.detail.value });
    this.checkCanSubmit();
  },

  onRemarkInput(e) {
    this.setData({ remark: e.detail.value });
  },

  checkCanSubmit() {
    const { selectedSlot, studentName, studentPhone } = this.data;
    const canSubmit = selectedSlot && studentName.trim() && studentPhone.trim();
    this.setData({ canSubmit });
  },

  async submitAppointment() {
    const { selectedSlot, studentName, studentPhone, remark, coachId, courseId } = this.data;

    if (!selectedSlot || !studentName.trim() || !studentPhone.trim()) {
      wx.showToast({ title: '请填写完整信息', icon: 'none' });
      return;
    }

    // 简单的手机号验证
    if (!/^1[3-9]\d{9}$/.test(studentPhone)) {
      wx.showToast({ title: '请输入正确的手机号', icon: 'none' });
      return;
    }

    wx.showLoading({ title: '提交中...' });

    try {
      const res = await wx.cloud.callFunction({
        name: 'createAppointment',
        data: {
          coachId: coachId,
          courseId: courseId,
          timeSlot: selectedSlot,
          studentName: studentName.trim(),
          studentPhone: studentPhone.trim(),
          remark: remark.trim()
        }
      });

      wx.hideLoading();

      if (res.result.success) {
        // 跳转到支付成功页面
        wx.redirectTo({
          url: `/pages/paySuccess/index?appointmentId=${res.result.data.appointmentId}&needDeposit=${res.result.data.needDeposit}&depositAmount=${res.result.data.depositAmount}&coachWechat=${res.result.data.coachWechat}`
        });
      } else {
        wx.showToast({
          title: res.result.error || '提交失败',
          icon: 'none'
        });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('提交申请失败：', error);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    }
  }
});