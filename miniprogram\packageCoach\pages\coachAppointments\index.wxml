<view class="appointments-container">
  <view class="header">
    <text class="title">预约管理</text>
  </view>
  
  <view class="tabs">
    <button 
      class="tab-btn {{currentTab === 'pending' ? 'active' : ''}}" 
      bindtap="switchTab" 
      data-tab="pending"
    >
      待确认 {{pendingCount > 0 ? '(' + pendingCount + ')' : ''}}
    </button>
    <button 
      class="tab-btn {{currentTab === 'confirmed' ? 'active' : ''}}" 
      bindtap="switchTab" 
      data-tab="confirmed"
    >
      已确认
    </button>
    <button 
      class="tab-btn {{currentTab === 'completed' ? 'active' : ''}}" 
      bindtap="switchTab" 
      data-tab="completed"
    >
      已完成
    </button>
    <button 
      class="tab-btn {{currentTab === 'cancelled' ? 'active' : ''}}" 
      bindtap="switchTab" 
      data-tab="cancelled"
    >
      已取消
    </button>
  </view>
  
  <view class="appointments-list">
    <view wx:if="{{loading}}" class="loading">加载中...</view>
    <view wx:elif="{{errorMsg}}" class="error-msg">{{errorMsg}}</view>
    <view wx:elif="{{filteredAppointments.length === 0}}" class="empty-tip">
      <text class="empty-text">暂无{{tabNames[currentTab]}}的预约</text>
    </view>
    <block wx:else>
      <view 
        wx:for="{{filteredAppointments}}" 
        wx:key="_id" 
        class="appointment-card"
        bindtap="goToDetail" 
        data-id="{{item._id}}"
      >
        <view class="card-header">
          <view class="student-info">
            <text class="student-name">{{item.studentName}}</text>
            <text class="student-phone">{{item.studentPhone}}</text>
          </view>
          <text class="status-tag status-{{item.statusClass}}">{{item.statusText}}</text>
        </view>
        
        <view class="card-content">
          <view class="info-row">
            <text class="label">课程：</text>
            <text class="value">{{item.courseTitle}}</text>
          </view>
          <view class="info-row">
            <text class="label">时间：</text>
            <text class="value">{{item.timeSlot.date}} {{item.timeSlot.start}}-{{item.timeSlot.end}}</text>
          </view>
          <view class="info-row">
            <text class="label">价格：</text>
            <text class="value">¥{{item.price}}</text>
          </view>
          <view class="info-row" wx:if="{{item.remark}}">
            <text class="label">备注：</text>
            <text class="value">{{item.remark}}</text>
          </view>
        </view>
        
        <view class="card-actions" wx:if="{{item.actions.length > 0}}">
          <block wx:for="{{item.actions}}" wx:key="*this" wx:for-item="action">
            <button 
              class="action-btn {{action.type}}" 
              bindtap="handleAction" 
              data-action="{{action.action}}" 
              data-id="{{item._id}}"
              data-student-name="{{item.studentName}}"
            >
              {{action.text}}
            </button>
          </block>
        </view>
      </view>
    </block>
  </view>
</view>
