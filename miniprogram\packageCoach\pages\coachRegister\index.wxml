<view class="register-container">
  <view class="header">
    <text class="title">教练注册</text>
    <text class="subtitle">填写以下信息申请成为认证教练</text>
  </view>

  <view class="form-section">
    <view class="section-title">基本信息</view>
    
    <view class="form-item">
      <text class="form-label">姓名 *</text>
      <input 
        class="form-input"
        placeholder="请输入真实姓名"
        value="{{formData.name}}"
        bindinput="onInputChange"
        data-field="name"
      />
    </view>

    <view class="form-item">
      <text class="form-label">手机号 *</text>
      <input 
        class="form-input"
        placeholder="请输入手机号"
        type="number"
        value="{{formData.phone}}"
        bindinput="onInputChange"
        data-field="phone"
        maxlength="11"
      />
    </view>

    <view class="form-item">
      <text class="form-label">密码 *</text>
      <input 
        class="form-input"
        placeholder="请设置登录密码"
        type="password"
        value="{{formData.password}}"
        bindinput="onInputChange"
        data-field="password"
      />
    </view>

    <view class="form-item">
      <text class="form-label">确认密码 *</text>
      <input 
        class="form-input"
        placeholder="请再次输入密码"
        type="password"
        value="{{formData.confirmPassword}}"
        bindinput="onInputChange"
        data-field="confirmPassword"
      />
    </view>

    <view class="form-item">
      <text class="form-label">所在城市 *</text>
      <input 
        class="form-input"
        placeholder="如：北京"
        value="{{formData.city}}"
        bindinput="onInputChange"
        data-field="city"
      />
    </view>

    <view class="form-item">
      <text class="form-label">常驻岩馆</text>
      <input 
        class="form-input"
        placeholder="如：某某攀岩馆"
        value="{{formData.gym}}"
        bindinput="onInputChange"
        data-field="gym"
      />
    </view>
  </view>

  <view class="form-section">
    <view class="section-title">攀岩水平</view>
    
    <view class="form-item">
      <text class="form-label">抱石水平</text>
      <input 
        class="form-input"
        placeholder="如：V8"
        value="{{formData.boulderingLevel}}"
        bindinput="onInputChange"
        data-field="boulderingLevel"
      />
    </view>

    <view class="form-item">
      <text class="form-label">先锋水平</text>
      <input 
        class="form-input"
        placeholder="如：5.12a"
        value="{{formData.leadLevel}}"
        bindinput="onInputChange"
        data-field="leadLevel"
      />
    </view>
  </view>

  <view class="form-section">
    <view class="section-title">教学信息</view>
    
    <view class="form-item">
      <text class="form-label">教学专长</text>
      <input 
        class="form-input"
        placeholder="如：动态技巧,耐力训练"
        value="{{formData.specialty}}"
        bindinput="onInputChange"
        data-field="specialty"
      />
    </view>

    <view class="form-item">
      <text class="form-label">个人简介</text>
      <textarea 
        class="form-textarea"
        placeholder="请简单介绍您的攀岩经历和教学经验"
        value="{{formData.intro}}"
        bindinput="onInputChange"
        data-field="intro"
        maxlength="200"
      />
      <text class="char-count">{{formData.intro.length}}/200</text>
    </view>
  </view>

  <view class="form-section">
    <view class="section-title">联系方式 (选填)</view>
    <view class="section-desc">学员可通过课前咨询获取您的联系方式</view>

    <view class="form-item">
      <text class="form-label">微信号</text>
      <input
        class="form-input"
        placeholder="请输入您的微信号"
        value="{{formData.wechat}}"
        bindinput="onInputChange"
        data-field="wechat"
      />
    </view>

    <view class="form-item">
      <text class="form-label">小红书</text>
      <input
        class="form-input"
        placeholder="请输入小红书用户名或链接"
        value="{{formData.xiaohongshu}}"
        bindinput="onInputChange"
        data-field="xiaohongshu"
      />
    </view>

    <view class="form-item">
      <text class="form-label">视频号</text>
      <input
        class="form-input"
        placeholder="请输入视频号名称"
        value="{{formData.videoAccount}}"
        bindinput="onInputChange"
        data-field="videoAccount"
      />
    </view>

    <view class="form-item">
      <text class="form-label">联系电话</text>
      <input
        class="form-input"
        placeholder="请输入联系电话"
        value="{{formData.contactPhone}}"
        bindinput="onInputChange"
        data-field="contactPhone"
        type="number"
      />
    </view>
  </view>

  <view class="agreement-section">
    <label class="agreement-item" bindtap="toggleAgreement">
      <checkbox checked="{{agreed}}" color="#667eea" />
      <text class="agreement-text">我已阅读并同意</text>
      <text class="agreement-link" bindtap="viewAgreement">《教练服务协议》</text>
    </label>
  </view>

  <view class="bottom-actions">
    <button class="cancel-btn" bindtap="goBack">取消</button>
    <button 
      class="submit-btn {{canSubmit ? 'active' : ''}}" 
      bindtap="submitRegister"
      disabled="{{!canSubmit}}"
    >
      提交申请
    </button>
  </view>
</view>
