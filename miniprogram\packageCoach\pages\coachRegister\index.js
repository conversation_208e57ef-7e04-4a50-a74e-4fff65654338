// 教练注册页面
Page({
  data: {
    formData: {
      name: '',
      phone: '',
      password: '',
      confirmPassword: '',
      city: '',
      gym: '',
      boulderingLevel: '',
      leadLevel: '',
      specialty: '',
      intro: '',
      // 联系方式
      wechat: '',
      xiaohongshu: '',
      videoAccount: '',
      contactPhone: ''
    },
    agreed: false,
    canSubmit: false
  },

  onLoad() {
    // 页面加载
  },

  // 输入框变化处理
  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    const value = e.detail.value;
    
    this.setData({
      [`formData.${field}`]: value
    }, () => {
      this.checkCanSubmit();
    });
  },

  // 切换协议同意状态
  toggleAgreement() {
    this.setData({
      agreed: !this.data.agreed
    }, () => {
      this.checkCanSubmit();
    });
  },

  // 查看协议
  viewAgreement(e) {
    e.stopPropagation();
    wx.showModal({
      title: '教练服务协议',
      content: '这里是教练服务协议的内容...\n\n1. 教练需要提供真实有效的个人信息\n2. 教练需要按时履行预约课程\n3. 教练需要保证教学质量和安全\n4. 平台有权对教练进行审核和管理',
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  // 检查是否可以提交
  checkCanSubmit() {
    const { formData, agreed } = this.data;
    const requiredFields = ['name', 'phone', 'password', 'confirmPassword', 'city'];
    
    // 检查必填字段
    const hasRequiredFields = requiredFields.every(field => 
      formData[field] && formData[field].trim() !== ''
    );
    
    // 检查密码一致性
    const passwordMatch = formData.password === formData.confirmPassword;
    
    // 检查手机号格式
    const phoneValid = /^1[3-9]\d{9}$/.test(formData.phone);
    
    // 检查密码长度
    const passwordValid = formData.password.length >= 6;
    
    const canSubmit = hasRequiredFields && passwordMatch && phoneValid && passwordValid && agreed;
    
    this.setData({ canSubmit });
  },

  // 提交注册
  async submitRegister() {
    if (!this.data.canSubmit) return;
    
    const { formData } = this.data;
    
    // 验证表单
    if (formData.password !== formData.confirmPassword) {
      wx.showToast({
        title: '两次密码不一致',
        icon: 'none'
      });
      return;
    }
    
    if (!/^1[3-9]\d{9}$/.test(formData.phone)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      });
      return;
    }
    
    if (formData.password.length < 6) {
      wx.showToast({
        title: '密码至少6位',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({ title: '提交中...' });
    
    try {
      const res = await wx.cloud.callFunction({
        name: 'coachRegister',
        data: {
          name: formData.name.trim(),
          phone: formData.phone.trim(),
          password: formData.password,
          city: formData.city.trim(),
          gym: formData.gym.trim(),
          boulderingLevel: formData.boulderingLevel.trim(),
          leadLevel: formData.leadLevel.trim(),
          specialty: formData.specialty.trim(),
          intro: formData.intro.trim()
        }
      });
      
      if (res.result.success) {
        wx.showModal({
          title: '申请提交成功',
          content: '您的教练申请已提交，我们会在1-3个工作日内审核，请耐心等待。',
          showCancel: false,
          confirmText: '知道了',
          success: () => {
            wx.navigateBack();
          }
        });
      } else {
        wx.showToast({
          title: res.result.error || '注册失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('注册失败：', error);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 返回上一页
  goBack() {
    wx.navigateBack();
  }
});
