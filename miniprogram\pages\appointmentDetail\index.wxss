/* 预约详情页样式 */
.detail-container {
  background: #f6f6f6;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.header {
  background: #fff;
  padding: 32rpx;
  text-align: center;
  margin-bottom: 16rpx;
}

.title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.loading, .error-msg {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding: 80rpx 0;
}

.error-msg {
  color: #e54545;
}

.status-card, .course-card, .student-card, .time-card {
  background: #fff;
  margin-bottom: 16rpx;
  padding: 32rpx;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.status-text {
  font-size: 28rpx;
  font-weight: 600;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
}

.status-pending {
  background: #fff7e6;
  color: #fa8c16;
}

.status-confirmed {
  background: #e6f7ff;
  color: #1890ff;
}

.status-completed {
  background: #f6ffed;
  color: #52c41a;
}

.status-cancelled {
  background: #fff2f0;
  color: #ff4d4f;
}

.appointment-id {
  font-size: 24rpx;
  color: #999;
}

.progress-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.step-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f0f0f0;
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.progress-step.active .step-icon {
  background: #007aff;
  color: #fff;
}

.step-text {
  font-size: 22rpx;
  color: #999;
}

.progress-step.active .step-text {
  color: #007aff;
}

.progress-line {
  flex: 1;
  height: 2rpx;
  background: #f0f0f0;
  margin: 0 16rpx;
  margin-top: -24rpx;
}

.progress-line.active {
  background: #007aff;
}

.card-header {
  margin-bottom: 24rpx;
}

.card-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.coach-section {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.coach-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 24rpx;
}

.coach-details {
  flex: 1;
}

.coach-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 4rpx;
}

.coach-gym {
  font-size: 24rpx;
  color: #666;
  display: block;
}

.detail-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.label {
  font-size: 26rpx;
  color: #666;
  min-width: 120rpx;
}

.value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}

.deposit-info {
  color: #fa8c16;
  font-weight: 500;
}

.actions-card {
  background: #fff;
  padding: 32rpx;
  display: flex;
  gap: 16rpx;
  justify-content: center;
}

.action-btn {
  flex: 1;
  border-radius: 24rpx;
  padding: 20rpx 0;
  font-size: 28rpx;
  border: none;
}

.action-btn.primary {
  background: #007aff;
  color: #fff;
}

.action-btn.secondary {
  background: #f0f0f0;
  color: #333;
}