// 教练端选择页面
Page({
  data: {
    
  },

  onLoad() {
    // 页面加载时的逻辑
  },

  // 跳转到教练注册页面
  goToRegister() {
    wx.showModal({
      title: '教练注册',
      content: '您将跳转到教练注册页面，填写个人信息申请成为教练。',
      confirmText: '继续',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/packageCoach/pages/coachRegister/index'
          });
        }
      }
    });
  },

  // 跳转到教练登录页面
  goToLogin() {
    wx.navigateTo({
      url: '/packageCoach/pages/coachLogin/index'
    });
  },

  // 返回首页
  goBack() {
    wx.navigateBack({
      delta: 1
    });
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: '攀岩教练平台 - 教练端',
      path: '/packageCoach/pages/coachEntry/index',
      imageUrl: '../../images/share-coach.png'
    };
  }
});
