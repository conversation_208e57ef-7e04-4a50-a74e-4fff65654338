/* 教练端选择页面样式 */
.entry-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 32rpx 32rpx 32rpx;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.logo-section {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.logo {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 24rpx;
  border-radius: 50%;
  background: rgba(255,255,255,0.2);
  padding: 20rpx;
}

.title {
  font-size: 48rpx;
  font-weight: 700;
  color: #fff;
  margin-bottom: 12rpx;
  display: block;
}

.subtitle {
  font-size: 28rpx;
  color: rgba(255,255,255,0.8);
  display: block;
}

.options-section {
  margin-bottom: 40rpx;
}

.option-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 32rpx 24rpx;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.option-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.option-card:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.15);
}

.option-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.register-icon {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
}

.login-icon {
  background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
}

.icon-text {
  font-size: 36rpx;
}

.option-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.option-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.option-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 16rpx;
  line-height: 1.4;
}

.option-features {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.feature-item {
  font-size: 22rpx;
  color: #999;
  line-height: 1.3;
}

.option-arrow {
  margin-left: 16rpx;
  flex-shrink: 0;
}

.arrow-icon {
  font-size: 32rpx;
  color: #667eea;
  font-weight: 600;
}

.info-section {
  margin-bottom: 40rpx;
}

.info-card {
  background: rgba(255,255,255,0.15);
  border-radius: 16rpx;
  padding: 24rpx;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255,255,255,0.2);
}

.info-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #fff;
  margin-bottom: 12rpx;
  display: block;
}

.info-content {
  font-size: 24rpx;
  color: rgba(255,255,255,0.9);
  line-height: 1.5;
  display: block;
}

.bottom-actions {
  display: flex;
  justify-content: center;
  padding-top: 20rpx;
}

.back-btn {
  background: rgba(255,255,255,0.2);
  color: #fff;
  border: 1rpx solid rgba(255,255,255,0.3);
  border-radius: 50rpx;
  padding: 16rpx 32rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  backdrop-filter: blur(10rpx);
}

.back-btn:active {
  background: rgba(255,255,255,0.3);
  transform: scale(0.95);
}

.back-icon {
  font-size: 24rpx;
  font-weight: 600;
}

/* 响应式优化 */
@media (max-width: 375px) {
  .option-card {
    padding: 24rpx 20rpx;
  }
  
  .option-icon {
    width: 60rpx;
    height: 60rpx;
    margin-right: 16rpx;
  }
  
  .icon-text {
    font-size: 28rpx;
  }
  
  .option-title {
    font-size: 28rpx;
  }
  
  .option-desc {
    font-size: 22rpx;
  }
  
  .feature-item {
    font-size: 20rpx;
  }
}
