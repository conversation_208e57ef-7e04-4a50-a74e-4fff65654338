const cloud = require('wx-server-sdk')
cloud.init()
const db = cloud.database()

exports.main = async (event, context) => {
  try {
    console.log('开始获取城市列表...')

    // 从教练数据中获取所有不重复的城市
    const coachRes = await db.collection('coaches')
      .field({
        city: true,
        status: true
      })
      .where({
        status: db.neq('rejected') // 排除被拒绝的教练
      })
      .get()

    console.log('查询到的教练数量：', coachRes.data.length)

    if (coachRes.data.length === 0) {
      console.log('没有找到教练数据，返回空数组')
      return {
        success: true,
        data: [],
        message: '暂无教练数据'
      }
    }

    // 提取所有城市并去重
    const cities = [...new Set(
      coachRes.data
        .map(coach => coach.city)
        .filter(city => city && city.trim() !== '') // 过滤空值
    )].sort() // 按字母排序

    console.log('获取到的城市列表：', cities)

    return {
      success: true,
      data: cities,
      count: cities.length
    }
  } catch (error) {
    console.error('获取城市列表失败：', error)

    // 返回默认城市列表作为兜底
    const defaultCities = ['北京', '上海', '广州', '深圳', '杭州', '成都', '重庆', '西安', '武汉']

    return {
      success: true,
      data: defaultCities,
      error: error.message,
      fallback: true
    }
  }
}
