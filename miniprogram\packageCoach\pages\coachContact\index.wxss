/* 联系方式页面样式 */
page {
  background: #f8fafc;
}

.contact-container {
  min-height: 100vh;
  padding-bottom: 40rpx;
}

/* 页面头部 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  background: #fff;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.08);
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64rpx;
  height: 64rpx;
  background: #f8fafc;
  border-radius: 50%;
  border: none;
  padding: 0;
}

.back-icon {
  font-size: 32rpx;
  color: #333;
}

.page-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.placeholder {
  width: 64rpx;
}

/* 加载和错误状态 */
.loading-section, .error-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 120rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 24rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text, .error-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 24rpx;
}

.retry-btn {
  padding: 16rpx 32rpx;
  background: #667eea;
  color: #fff;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: none;
}

/* 内容区域 */
.content {
  padding: 32rpx;
}

/* 教练信息 */
.coach-info {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
}

.coach-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 16rpx;
  margin-right: 24rpx;
  background: #f8fafc;
}

.coach-details {
  flex: 1;
}

.coach-name {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.coach-gym {
  font-size: 24rpx;
  color: #666;
}

/* 联系方式列表 */
.contact-list {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
}

.contact-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 32rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f8fafc;
}

.contact-item:last-child {
  border-bottom: none;
}

.contact-icon {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  margin-right: 24rpx;
  font-size: 32rpx;
}

.wechat-icon {
  background: #e8f5e8;
}

.phone-icon {
  background: #e8f0ff;
}

.xiaohongshu-icon {
  background: #ffe8e8;
}

.video-icon {
  background: #fff0e8;
}

.contact-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.contact-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.contact-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.copy-btn, .call-btn {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: none;
  min-width: 80rpx;
}

.copy-btn {
  background: #f0f4ff;
  color: #667eea;
}

.call-btn {
  background: #e8f5e8;
  color: #34c759;
}

/* 无联系方式状态 */
.no-contact {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0;
}

.no-contact-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  opacity: 0.5;
}

.no-contact-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.no-contact-desc {
  font-size: 24rpx;
  color: #999;
}

/* 温馨提示 */
.tips-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
}

.tips-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.tip-item {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}
