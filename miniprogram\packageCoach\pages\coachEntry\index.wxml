<view class="entry-container">
  <view class="header">
    <view class="logo-section">
      <view class="logo">🧗‍♂️</view>
      <text class="title">教练端</text>
      <text class="subtitle">选择您的操作</text>
    </view>
  </view>

  <view class="options-section">
    <view class="option-card" bindtap="goToRegister">
      <view class="option-icon register-icon">
        <text class="icon-text">📝</text>
      </view>
      <view class="option-content">
        <text class="option-title">注册教练账户</text>
        <text class="option-desc">填写个人信息，申请成为教练</text>
        <view class="option-features">
          <text class="feature-item">• 创建个人档案</text>
          <text class="feature-item">• 设置课程价格</text>
          <text class="feature-item">• 管理预约时间</text>
        </view>
      </view>
      <view class="option-arrow">
        <text class="arrow-icon">→</text>
      </view>
    </view>

    <view class="option-card" bindtap="goToLogin">
      <view class="option-icon login-icon">
        <text class="icon-text">🔑</text>
      </view>
      <view class="option-content">
        <text class="option-title">教练登录</text>
        <text class="option-desc">已有教练账户，登录管理后台</text>
        <view class="option-features">
          <text class="feature-item">• 查看预约信息</text>
          <text class="feature-item">• 管理课程信息</text>
          <text class="feature-item">• 设置可约时间</text>
        </view>
      </view>
      <view class="option-arrow">
        <text class="arrow-icon">→</text>
      </view>
    </view>
  </view>

  <view class="info-section">
    <view class="info-card">
      <text class="info-title">💡 温馨提示</text>
      <text class="info-content">新教练需要先注册后才能开始登录。已注册教练可直接登录管理后台。</text>
    </view>
  </view>

  <view class="bottom-actions">
    <button class="back-btn" bindtap="goBack">
      <text class="back-icon">←</text>
      <text>返回首页</text>
    </button>
  </view>
</view>
