// index.js
Page({
  data: {
    // 搜索和筛选条件
    keyword: '',
    selectedCityIndex: 0,
    selectedClimbingTypeIndex: 0,
    selectedPriceIndex: 0,
    selectedSpecialtyIndex: 0,

    // 筛选选项
    cityList: ['全部', '北京', '上海', '广州', '深圳', '杭州', '成都', '重庆', '西安', '武汉'],
    climbingTypes: ['全部', '室内抱石', '室内运动攀', '户外运动攀', '传统攀', '野外抱石', '深水抱石'],
    priceRanges: ['全部', '¥500以下', '¥500-800', '¥800以上'],
    specialties: ['全部', '入门教学', '技术提升', '心理辅导', '动态技巧', '耐力训练', '力量训练'],

    // 教练数据
    coachList: [],
    loading: false,
    errorMsg: '',

    // 防抖定时器
    searchTimer: null
  },

  onLoad() {
    this.loadCityList();
    this.searchCoaches(); // 初始加载所有教练
  },

  // 关键词输入
  onKeywordChange(e) {
    const keyword = e.detail.value;
    this.setData({ keyword });
    this.debouncedSearch();
  },

  // 城市选择
  onCityChange(e) {
    const index = parseInt(e.detail.value);
    this.setData({ selectedCityIndex: index });
    this.searchCoaches();
  },

  // 攀岩类型选择
  onClimbingTypeChange(e) {
    const index = parseInt(e.detail.value);
    this.setData({ selectedClimbingTypeIndex: index });
    this.searchCoaches();
  },

  // 价格范围选择
  onPriceChange(e) {
    const index = parseInt(e.detail.value);
    this.setData({ selectedPriceIndex: index });
    this.searchCoaches();
  },

  // 专长选择
  onSpecialtyChange(e) {
    const index = parseInt(e.detail.value);
    this.setData({ selectedSpecialtyIndex: index });
    this.searchCoaches();
  },

  // 重置筛选条件
  resetFilters() {
    this.setData({
      keyword: '',
      selectedCityIndex: 0,
      selectedClimbingTypeIndex: 0,
      selectedPriceIndex: 0,
      selectedSpecialtyIndex: 0
    });
    this.searchCoaches();
  },

  // 防抖搜索
  debouncedSearch() {
    if (this.data.searchTimer) {
      clearTimeout(this.data.searchTimer);
    }
    const timer = setTimeout(() => {
      this.searchCoaches();
    }, 500);
    this.setData({ searchTimer: timer });
  },


  // 动态加载城市列表
  async loadCityList() {
    try {
      const res = await wx.cloud.callFunction({
        name: 'getCityList',
        data: {}
      });

      if (res.result && res.result.success) {
        const cities = ['全部', ...res.result.data];
        this.setData({ cityList: cities });
        console.log('城市列表加载成功：', cities);
      } else {
        console.warn('城市列表加载失败，使用默认列表');
        this.useDefaultCityList();
      }
    } catch (error) {
      console.error('加载城市列表失败：', error);

      // 检查是否是云函数未部署的错误
      if (error.errCode === -501000) {
        console.warn('getCityList 云函数未部署，使用默认城市列表');
      }

      this.useDefaultCityList();
    }
  },

  // 使用默认城市列表
  useDefaultCityList() {
    const defaultCities = [
      '全部', '北京', '上海', '广州', '深圳', '杭州',
      '成都', '重庆', '西安', '武汉', '苏州', '南京',
      '天津', '青岛', '大连', '厦门', '长沙', '郑州'
    ];

    this.setData({ cityList: defaultCities });
    console.log('使用默认城市列表：', defaultCities);
  },

  searchCoaches() {
    this.setData({ loading: true, errorMsg: '' });

    // 构建搜索参数
    const searchParams = {
      keyword: this.data.keyword,
      city: this.data.selectedCityIndex > 0 ? this.data.cityList[this.data.selectedCityIndex] : '',
      climbingType: this.data.selectedClimbingTypeIndex > 0 ? this.data.climbingTypes[this.data.selectedClimbingTypeIndex] : '',
      price: this.data.selectedPriceIndex > 0 ? this.data.priceRanges[this.data.selectedPriceIndex] : '',
      specialty: this.data.selectedSpecialtyIndex > 0 ? this.data.specialties[this.data.selectedSpecialtyIndex] : '',
      sortBy: 'rating',
      page: 1,
      pageSize: 50
    };

    console.log('搜索参数：', searchParams);

    wx.cloud.callFunction({
      name: 'searchCoaches',
      data: searchParams,
      success: res => {
        console.log('搜索教练结果：', res.result);
        if (res.result.success) {
          let list = res.result.data || [];

          // 数据处理
          list = list.map(item => ({
            ...item,
            avatar: item.avatar || 'cloud://miniprogram-15-7g0tuaph2c2c4e96.6d69-miniprogram-15-7g0tuaph2c2c4e96-1329434144/default-avatar.png',
            specialty: Array.isArray(item.specialty) ? item.specialty.join('、') : item.specialty || '攀岩教学',
            rating: item.rating ? item.rating.toFixed(1) : '0.0',
            reviewCount: item.reviewCount || 0,
            price: item.price || 680
          }));

          console.log('处理后的教练列表：', list);
          this.setData({
            coachList: list,
            loading: false,
            errorMsg: ''
          });

          // 动态更新城市列表
          this.updateCityListFromResults(list);
        } else {
          console.error('搜索失败：', res.result.error);
          this.setData({
            coachList: [],
            loading: false,
            errorMsg: res.result.error || '加载失败，请重试'
          });
        }
      },
      fail: err => {
        console.error('调用云函数失败：', err);
        this.setData({
          coachList: [],
          loading: false,
          errorMsg: '网络错误，请重试'
        });
      }
    });
  },

  // 从搜索结果更新城市列表
  updateCityListFromResults(coachList) {
    if (coachList.length > 0) {
      const cities = [...new Set(
        coachList.map(coach => coach.city)
          .filter(city => city && city.trim() !== '')
      )].sort();

      if (cities.length > 0) {
        const currentCityList = this.data.cityList;
        const newCities = cities.filter(city => !currentCityList.includes(city));

        if (newCities.length > 0) {
          const updatedCityList = [...currentCityList, ...newCities];
          this.setData({ cityList: updatedCityList });
          console.log('更新城市列表：', updatedCityList);
        }
      }
    }
  },
  // 头像加载错误处理
  onAvatarError(e) {
    const index = e.currentTarget.dataset.index;
    const updateKey = `coachList[${index}].avatar`;
    this.setData({
      [updateKey]: 'cloud://miniprogram-15-7g0tuaph2c2c4e96.6d69-miniprogram-15-7g0tuaph2c2c4e96-1329434144/default-avatar.png'
    });
  },

  // 跳转到教练详情
  goToCoachDetail(e) {
    const coachId = e.currentTarget.dataset.id;
    console.log('点击教练id', coachId);
    wx.navigateTo({
      url: `/pages/coachDetail/index?id=${coachId}`
    });
  },

  // 跳转到我的预约
  goToMyAppointments() {
    wx.navigateTo({
      url: '/pages/myAppointments/index'
    });
  },

  // 跳转到教练端选择页面
  goToCoachLogin() {
    wx.navigateTo({
      url: '/packageCoach/pages/coachEntry/index'
    });
  },

  // 跳转到测试页面
  goToTest() {
    wx.navigateTo({
      url: '/pages/test/test'
    });
  },






});
