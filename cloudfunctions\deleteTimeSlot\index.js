const cloud = require('wx-server-sdk')
cloud.init()
const db = cloud.database()

exports.main = async (event, context) => {
  const { slotId } = event
  
  // 获取用户openid
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  if (!slotId) {
    return {
      success: false,
      error: '时间段ID不能为空'
    }
  }
  
  try {
    // 获取教练信息
    const coachRes = await db.collection('coaches')
      .where({ openid: openid })
      .get()
    
    if (coachRes.data.length === 0) {
      return {
        success: false,
        error: '教练信息不存在'
      }
    }
    
    const coach = coachRes.data[0]
    const coachId = coach._id
    
    // 获取要删除的时间段信息
    const slotRes = await db.collection('coach_availability').doc(slotId).get()
    if (!slotRes.data) {
      return {
        success: false,
        error: '时间段不存在'
      }
    }
    
    const slot = slotRes.data
    
    // 验证权限
    if (slot.coachId !== coachId) {
      return {
        success: false,
        error: '无权限操作此时间段'
      }
    }
    
    // 检查时间段状态
    if (slot.status === 'booked') {
      return {
        success: false,
        error: '已预约的时间段不能删除，请先取消预约'
      }
    }
    
    // 删除时间段
    await db.collection('coach_availability').doc(slotId).remove()
    
    return {
      success: true,
      message: '时间段删除成功'
    }
  } catch (error) {
    console.error('删除时间段失败：', error)
    return {
      success: false,
      error: error.message
    }
  }
}
