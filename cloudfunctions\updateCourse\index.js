const cloud = require('wx-server-sdk')
cloud.init()
const db = cloud.database()

exports.main = async (event, context) => {
  const { courseId, courseData } = event
  
  // 获取用户openid
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  if (!courseId || !courseData) {
    return {
      success: false,
      error: '参数不完整'
    }
  }
  
  try {
    // 获取教练信息
    const coachRes = await db.collection('coaches')
      .where({ openid: openid })
      .get()
    
    if (coachRes.data.length === 0) {
      return {
        success: false,
        error: '教练信息不存在'
      }
    }
    
    const coach = coachRes.data[0]
    const coachId = coach._id
    
    // 获取课程信息并验证权限
    const courseRes = await db.collection('coach_courses').doc(courseId).get()
    if (!courseRes.data) {
      return {
        success: false,
        error: '课程不存在'
      }
    }
    
    if (courseRes.data.coachId !== coachId) {
      return {
        success: false,
        error: '无权限操作此课程'
      }
    }
    
    // 检查课程名称是否与其他课程重复
    if (courseData.name !== courseRes.data.name) {
      const existingCourse = await db.collection('coach_courses')
        .where({
          coachId: coachId,
          name: courseData.name,
          _id: db.command.neq(courseId)
        })
        .get()
      
      if (existingCourse.data.length > 0) {
        return {
          success: false,
          error: '课程名称已存在'
        }
      }
    }
    
    // 更新课程
    await db.collection('coach_courses').doc(courseId).update({
      data: {
        name: courseData.name,
        type: courseData.type,
        duration: courseData.duration,
        price: courseData.price,
        needDeposit: courseData.needDeposit || false,
        deposit: courseData.deposit || 0,
        description: courseData.description || '',
        outline: courseData.outline || '',
        targetAudience: courseData.targetAudience || '',
        status: courseData.status || 'active',
        updatedAt: new Date()
      }
    })
    
    return {
      success: true,
      message: '课程更新成功'
    }
  } catch (error) {
    console.error('更新课程失败：', error)
    return {
      success: false,
      error: error.message
    }
  }
}
