Page({
  data: {
    coach: {},
    coachId: ''
  },

  onLoad(options) {
    this.setData({ coachId: options.coachId });
    this.loadCoachInfo(options.coachId);
  },

  async loadCoachInfo(coachId) {
    try {
      const res = await wx.cloud.callFunction({
        name: 'getCoachDetails',
        data: { coachId: coachId }
      });

      if (res.result.success) {
        this.setData({
          coach: res.result.data.coach
        });
      }
    } catch (error) {
      console.error('获取教练信息失败：', error);
    }
  },

  copyWechat(e) {
    const wechat = e.currentTarget.dataset.wechat;
    wx.setClipboardData({
      data: wechat,
      success: () => {
        wx.showToast({
          title: '微信号已复制',
          icon: 'success'
        });
      }
    });
  },

  goBack() {
    wx.navigateBack();
  }
});