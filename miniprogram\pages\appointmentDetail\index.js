Page({
  data: {
    appointmentId: '',
    appointment: null,
    coach: null,
    loading: false,
    errorMsg: ''
  },

  onLoad(options) {
    this.setData({ appointmentId: options.id || '' });
    if (this.data.appointmentId) {
      this.loadAppointmentDetail();
    } else {
      this.setData({ errorMsg: '缺少预约ID' });
    }
  },

  async loadAppointmentDetail() {
    this.setData({ loading: true, errorMsg: '' });

    try {
      const res = await wx.cloud.callFunction({
        name: 'getAppointmentDetail',
        data: {
          appointmentId: this.data.appointmentId
        }
      });

      if (res.result.success) {
        const appointment = this.processAppointment(res.result.data.appointment);
        const coach = res.result.data.coach;

        this.setData({
          appointment,
          coach: coach || {
            name: '未知教练',
            avatar: 'cloud://miniprogram-15-7g0tuaph2c2c4e96.6d69-miniprogram-15-7g0tuaph2c2c4e96-1329434144/default-avatar.png',
            gym: '待确认'
          },
          loading: false
        });
      } else {
        this.setData({
          loading: false,
          errorMsg: res.result.error || '加载失败'
        });
      }
    } catch (error) {
      console.error('获取预约详情失败：', error);
      this.setData({
        loading: false,
        errorMsg: '网络错误，请重试'
      });
    }
  },

  processAppointment(appointment) {
    // 处理状态文本和样式
    const statusMap = {
      '待确认': { text: '待确认', class: 'pending', step: 1 },
      '已确认': { text: '已确认', class: 'confirmed', step: 2 },
      '已完成': { text: '已完成', class: 'completed', step: 3 },
      '已取消': { text: '已取消', class: 'cancelled', step: 1 }
    };

    const statusInfo = statusMap[appointment.status] || { text: appointment.status, class: 'unknown', step: 1 };

    // 处理定金状态文本
    const depositStatusMap = {
      '未支付': '未支付',
      '已支付': '已支付',
      '已退回': '已退回',
      '已结算': '已结算',
      '无需支付': '无需支付'
    };

    // 格式化时间
    const formatTime = (dateStr) => {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
    };

    // 确定可用操作
    const actions = [];
    if (appointment.status === '待确认') {
      actions.push({ action: 'cancel', text: '取消预约', type: 'secondary' });
      actions.push({ action: 'contact', text: '联系教练', type: 'primary' });
    } else if (appointment.status === '已确认') {
      actions.push({ action: 'contact', text: '联系教练', type: 'primary' });
    } else if (appointment.status === '已完成') {
      if (!appointment.hasReviewed) {
        actions.push({ action: 'review', text: '去评价', type: 'primary' });
      }
      actions.push({ action: 'rebook', text: '再次预约', type: 'secondary' });
    } else if (appointment.status === '已取消') {
      actions.push({ action: 'rebook', text: '再次预约', type: 'primary' });
    }

    return {
      ...appointment,
      statusText: statusInfo.text,
      statusClass: statusInfo.class,
      step: statusInfo.step,
      depositStatusText: depositStatusMap[appointment.depositStatus] || appointment.depositStatus,
      createdAtText: formatTime(appointment.createdAt),
      confirmedAtText: formatTime(appointment.confirmedAt),
      completedAtText: formatTime(appointment.completedAt),
      actions: actions
    };
  },

  handleAction(e) {
    const action = e.currentTarget.dataset.action;

    switch (action) {
      case 'cancel':
        this.cancelAppointment();
        break;
      case 'contact':
        this.contactCoach();
        break;
      case 'review':
        this.goToReview();
        break;
      case 'rebook':
        this.rebookAppointment();
        break;
    }
  },

  cancelAppointment() {
    wx.showModal({
      title: '确认取消',
      content: '确定要取消这个预约吗？',
      success: (res) => {
        if (res.confirm) {
          // TODO: 调用取消预约的云函数
          wx.showToast({ title: '取消成功', icon: 'success' });
          // 重新加载数据
          this.loadAppointmentDetail();
        }
      }
    });
  },

  contactCoach() {
    if (this.data.coach && this.data.appointment) {
      wx.navigateTo({
        url: `/pages/message/index?coachId=${this.data.appointment.coachId}`
      });
    }
  },

  goToReview() {
    wx.navigateTo({
      url: `/pages/reviewSubmit/index?appointmentId=${this.data.appointmentId}`
    });
  },

  rebookAppointment() {
    if (this.data.appointment) {
      wx.navigateTo({
        url: `/pages/appointment/index?coachId=${this.data.appointment.coachId}`
      });
    }
  }
});