// 云函数入口文件
const cloud = require('wx-server-sdk')
cloud.init()
const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const { coachId, courseId, date } = event
  
  if (!coachId) {
    return {
      success: false,
      error: '缺少教练ID参数'
    }
  }
  
  try {
    // 获取教练信息
    const coachRes = await db.collection('coaches').doc(coachId).get()
    if (!coachRes.data) {
      return {
        success: false,
        error: '教练不存在'
      }
    }
    
    // 生成未来7天的可预约时间段（示例数据）
    const availableSlots = generateAvailableSlots(coachId, date)
    
    return {
      success: true,
      data: {
        slots: availableSlots,
        coach: coachRes.data
      }
    }
  } catch (error) {
    console.error('获取可预约时间失败：', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// 生成可预约时间段（示例实现）
function generateAvailableSlots(coachId, targetDate) {
  const slots = []
  const today = new Date()
  
  // 生成未来7天的时间段
  for (let i = 1; i <= 7; i++) {
    const date = new Date(today)
    date.setDate(today.getDate() + i)
    
    const dateStr = date.toISOString().split('T')[0] // YYYY-MM-DD格式
    const dayOfWeek = date.getDay()
    
    // 跳过周日
    if (dayOfWeek === 0) continue
    
    // 工作日时间段
    if (dayOfWeek >= 1 && dayOfWeek <= 5) {
      slots.push({
        date: dateStr,
        start: '14:00',
        end: '15:30',
        isBooked: Math.random() > 0.7, // 随机已预约状态
        price: 680
      })
      slots.push({
        date: dateStr,
        start: '16:00',
        end: '17:30',
        isBooked: Math.random() > 0.8,
        price: 680
      })
    }
    
    // 周末时间段更多
    if (dayOfWeek === 6 || dayOfWeek === 7) {
      const timeSlots = [
        { start: '09:00', end: '10:30' },
        { start: '10:30', end: '12:00' },
        { start: '14:00', end: '15:30' },
        { start: '16:00', end: '17:30' },
        { start: '19:00', end: '20:30' }
      ]
      
      timeSlots.forEach(slot => {
        slots.push({
          date: dateStr,
          start: slot.start,
          end: slot.end,
          isBooked: Math.random() > 0.6,
          price: 680
        })
      })
    }
  }
  
  return slots
}
