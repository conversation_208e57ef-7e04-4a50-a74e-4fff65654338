/* 教练时间管理页样式 */
.schedule-container {
  background: #f6f6f6;
  min-height: 100vh;
}

.header {
  background: #667eea;
  padding: 32rpx;
  text-align: center;
}

.title {
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
}

.calendar-section {
  background: #fff;
  margin: 16rpx;
  border-radius: 12rpx;
  padding: 24rpx;
}

.calendar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.nav-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #f0f0f0;
  border: none;
  font-size: 32rpx;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
}

.month-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.weekday {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  padding: 12rpx 0;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8rpx;
}

.calendar-day {
  aspect-ratio: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
  position: relative;
  cursor: pointer;
}

.calendar-day.other-month {
  color: #ccc;
}

.calendar-day.today {
  background: #667eea;
  color: #fff;
}

.calendar-day.has-schedule {
  background: #e6f7ff;
}

.day-number {
  font-size: 26rpx;
  font-weight: 500;
}

.schedule-dots {
  position: absolute;
  bottom: 4rpx;
  right: 4rpx;
}

.dot-count {
  background: #ff4757;
  color: #fff;
  border-radius: 50%;
  width: 24rpx;
  height: 24rpx;
  font-size: 18rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.selected-date-section {
  background: #fff;
  margin: 16rpx;
  border-radius: 12rpx;
  padding: 24rpx;
}

.date-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.selected-date {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.add-schedule-btn {
  background: #667eea;
  color: #fff;
  border: none;
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
}

.time-slots {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.empty-tip {
  text-align: center;
  padding: 40rpx 0;
}

.empty-text {
  font-size: 26rpx;
  color: #999;
}

.time-slot-card {
  background: #f8f9fa;
  border-radius: 8rpx;
  padding: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.slot-info {
  flex: 1;
}

.slot-time {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 4rpx;
}

.slot-status {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

.status-available {
  background: #e6f7ff;
  color: #1890ff;
}

.status-booked {
  background: #fff7e6;
  color: #fa8c16;
}

.status-completed {
  background: #f6ffed;
  color: #52c41a;
}

.slot-actions {
  display: flex;
  gap: 12rpx;
}

.action-btn {
  border: none;
  border-radius: 16rpx;
  padding: 8rpx 16rpx;
  font-size: 22rpx;
}

.edit-btn {
  background: #667eea;
  color: #fff;
}

.delete-btn {
  background: #ff4757;
  color: #fff;
}

.view-btn {
  background: #28a745;
  color: #fff;
}

.legend-section {
  background: #fff;
  margin: 16rpx;
  border-radius: 12rpx;
  padding: 24rpx;
}

.legend-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.legend-items {
  display: flex;
  gap: 32rpx;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.legend-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
}

.legend-dot.available {
  background: #1890ff;
}

.legend-dot.booked {
  background: #fa8c16;
}

.legend-dot.completed {
  background: #52c41a;
}

.legend-text {
  font-size: 24rpx;
  color: #666;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.time-modal {
  background: #fff;
  border-radius: 12rpx;
  width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f0f0f0;
  border: none;
  font-size: 24rpx;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  padding: 32rpx;
}

.form-item {
  margin-bottom: 32rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 12rpx;
}

.time-picker {
  background: #f8f8f8;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  border: 2rpx solid #f8f8f8;
}

.repeat-options {
  display: flex;
  gap: 32rpx;
}

.repeat-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 28rpx;
  color: #333;
}

.modal-actions {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  padding: 24rpx 0;
  border: none;
  font-size: 28rpx;
}

.cancel-btn {
  background: #f8f8f8;
  color: #666;
}

.confirm-btn {
  background: #667eea;
  color: #fff;
}

.confirm-btn[disabled] {
  background: #ccc;
  color: #999;
}

/* 每周模板弹窗专用样式 */
.weekly-modal {
  background: #fff;
  border-radius: 16rpx;
  width: 90%;
  max-width: 700rpx;
  max-height: 85vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.2);
}

.weekdays-section {
  margin-bottom: 32rpx;
}

.section-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.weekdays-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12rpx;
}

.weekday-item {
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 16rpx 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  font-size: 26rpx;
  color: #495057;
  transition: all 0.2s;
  cursor: pointer;
}

.weekday-item.checked {
  background: #e3f2fd;
  border-color: #667eea;
  color: #667eea;
  font-weight: 600;
}

.weekday-item checkbox {
  transform: scale(0.9);
}

.time-templates {
  margin-bottom: 32rpx;
}

.templates-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.template-item {
  background: #f8f9fa;
  border: 1rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.time-range {
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex: 1;
}

.time-picker-small {
  background: #fff;
  border: 1rpx solid #dee2e6;
  border-radius: 8rpx;
  padding: 12rpx 16rpx;
  font-size: 26rpx;
  color: #495057;
  min-width: 120rpx;
  text-align: center;
}

.time-separator {
  font-size: 24rpx;
  color: #6c757d;
  font-weight: 500;
}

.remove-template-btn {
  background: #dc3545;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  padding: 8rpx 16rpx;
  font-size: 22rpx;
  margin-left: 16rpx;
}

.add-template-btn {
  background: #667eea;
  color: #fff;
  border: none;
  border-radius: 12rpx;
  padding: 16rpx 24rpx;
  font-size: 26rpx;
  width: 100%;
  margin-top: 8rpx;
}

.date-range-section {
  margin-bottom: 32rpx;
}

.date-range {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.date-picker {
  background: #f8f9fa;
  border: 1rpx solid #dee2e6;
  border-radius: 8rpx;
  padding: 16rpx 20rpx;
  font-size: 26rpx;
  color: #495057;
  flex: 1;
  text-align: center;
}

.date-separator {
  font-size: 24rpx;
  color: #6c757d;
  font-weight: 500;
}

/* 快速操作区域样式 */
.quick-actions {
  background: #fff;
  margin: 16rpx;
  border-radius: 12rpx;
  padding: 24rpx;
}

.actions-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.quick-buttons {
  display: flex;
  gap: 16rpx;
}

.quick-btn {
  flex: 1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border: none;
  border-radius: 12rpx;
  padding: 20rpx 16rpx;
  font-size: 26rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
  transition: all 0.2s;
}

.quick-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
}

/* 提示文字样式 */
.weekdays-tip, .templates-tip, .date-range-tip {
  margin-top: 12rpx;
  text-align: center;
}

.tip-text {
  font-size: 22rpx;
  color: #6c757d;
  line-height: 1.4;
}

/* 添加按钮图标 */
.add-template-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.add-icon {
  font-size: 28rpx;
  font-weight: 600;
}

/* 优化时间选择器样式 */
.time-picker-small:empty::before {
  content: attr(placeholder);
  color: #adb5bd;
}

/* 优化日期选择器样式 */
.date-picker:empty::before {
  content: attr(placeholder);
  color: #adb5bd;
}

/* 模态框内容区域滚动优化 */
.weekly-modal .modal-content {
  padding: 24rpx 32rpx;
  max-height: calc(85vh - 140rpx);
  overflow-y: auto;
}

/* 删除按钮优化 */
.remove-template-btn:active {
  background: #c82333;
  transform: scale(0.95);
}
