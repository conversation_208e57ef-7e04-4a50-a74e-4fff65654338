const cloud = require('wx-server-sdk')
cloud.init()
const db = cloud.database()

exports.main = async (event, context) => {
  const { appointmentId } = event
  
  // 获取用户openid
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  if (!appointmentId) {
    return {
      success: false,
      error: '缺少预约ID参数'
    }
  }
  
  try {
    // 获取预约信息
    const appointmentRes = await db.collection('appointments').doc(appointmentId).get()
    if (!appointmentRes.data) {
      return {
        success: false,
        error: '预约不存在'
      }
    }
    
    const appointment = appointmentRes.data
    
    // 验证用户权限
    if (appointment.studentId !== openid) {
      return {
        success: false,
        error: '无权限查看此预约'
      }
    }
    
    // 获取教练信息
    let coach = null
    try {
      const coachRes = await db.collection('coaches').doc(appointment.coachId).get()
      coach = coachRes.data
    } catch (error) {
      console.error('获取教练信息失败：', error)
    }
    
    return {
      success: true,
      data: {
        appointment: appointment,
        coach: coach
      }
    }
  } catch (error) {
    console.error('获取预约详情失败：', error)
    return {
      success: false,
      error: error.message
    }
  }
}
