const cloud = require('wx-server-sdk')
cloud.init()
const db = cloud.database()

exports.main = async (event, context) => {
  const { courseId } = event
  
  // 获取用户openid
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  if (!courseId) {
    return {
      success: false,
      error: '课程ID不能为空'
    }
  }
  
  try {
    // 获取教练信息
    const coachRes = await db.collection('coaches')
      .where({ openid: openid })
      .get()
    
    if (coachRes.data.length === 0) {
      return {
        success: false,
        error: '教练信息不存在'
      }
    }
    
    const coach = coachRes.data[0]
    const coachId = coach._id
    
    // 获取课程信息并验证权限
    const courseRes = await db.collection('coach_courses').doc(courseId).get()
    if (!courseRes.data) {
      return {
        success: false,
        error: '课程不存在'
      }
    }
    
    if (courseRes.data.coachId !== coachId) {
      return {
        success: false,
        error: '无权限操作此课程'
      }
    }
    
    // 检查是否有相关的预约
    const appointmentsRes = await db.collection('appointments')
      .where({
        coachId: coachId,
        courseId: courseId,
        status: db.command.in(['待确认', '已确认'])
      })
      .get()
    
    if (appointmentsRes.data.length > 0) {
      return {
        success: false,
        error: '该课程还有未完成的预约，无法删除'
      }
    }
    
    // 删除课程
    await db.collection('coach_courses').doc(courseId).remove()
    
    return {
      success: true,
      message: '课程删除成功'
    }
  } catch (error) {
    console.error('删除课程失败：', error)
    return {
      success: false,
      error: error.message
    }
  }
}
