/* 教练后台首页样式 */
.dashboard-container {
  background: #f6f6f6;
  min-height: 100vh;
  padding-bottom: 32rpx;
}

.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.coach-info {
  display: flex;
  align-items: center;
}

.coach-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 24rpx;
  border: 3rpx solid rgba(255,255,255,0.3);
}

.coach-details {
  flex: 1;
}

.coach-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
  display: block;
  margin-bottom: 4rpx;
}

.coach-gym {
  font-size: 24rpx;
  color: rgba(255,255,255,0.8);
  display: block;
}

.logout-btn {
  background: rgba(255,255,255,0.2);
  color: #fff;
  border: 1rpx solid rgba(255,255,255,0.3);
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
}

.stats-section, .monthly-section, .quick-actions, .recent-section {
  background: #fff;
  margin: 16rpx 32rpx;
  border-radius: 12rpx;
  padding: 32rpx;
}

.stats-title, .monthly-title, .actions-title, .recent-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

.stats-grid {
  display: flex;
  justify-content: space-between;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-number {
  font-size: 36rpx;
  font-weight: 600;
  color: #667eea;
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
  display: block;
}

.monthly-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.monthly-item {
  background: #f8f9fa;
  border-radius: 8rpx;
  padding: 24rpx;
  text-align: center;
}

.monthly-label {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.monthly-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  display: block;
}

.actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 24rpx;
}

.action-btn {
  background: #f8f9fa;
  border: none;
  border-radius: 12rpx;
  padding: 32rpx 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  transition: background 0.2s;
}

.action-btn:active {
  background: #e9ecef;
}

.action-icon {
  font-size: 48rpx;
  margin-bottom: 12rpx;
}

.action-text {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

.action-badge {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  background: #ff4757;
  color: #fff;
  border-radius: 50%;
  width: 32rpx;
  height: 32rpx;
  font-size: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.recent-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.recent-item {
  background: #f8f9fa;
  border-radius: 8rpx;
  padding: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.recent-info {
  flex: 1;
}

.recent-student {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 4rpx;
}

.recent-time {
  font-size: 24rpx;
  color: #666;
  display: block;
}

.recent-status {
  font-size: 22rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

.status-pending {
  background: #fff7e6;
  color: #fa8c16;
}

.status-confirmed {
  background: #e6f7ff;
  color: #1890ff;
}

.status-completed {
  background: #f6ffed;
  color: #52c41a;
}

.status-cancelled {
  background: #fff2f0;
  color: #ff4d4f;
}

.empty-tip {
  text-align: center;
  color: #999;
  font-size: 26rpx;
  padding: 40rpx 0;
}
