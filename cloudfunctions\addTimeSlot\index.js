const cloud = require('wx-server-sdk')
cloud.init()
const db = cloud.database()

exports.main = async (event, context) => {
  const { date, startTime, endTime, repeat } = event
  
  // 获取用户openid
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  if (!date || !startTime || !endTime) {
    return {
      success: false,
      error: '参数不完整'
    }
  }
  
  // 验证时间格式
  if (startTime >= endTime) {
    return {
      success: false,
      error: '开始时间必须早于结束时间'
    }
  }
  
  try {
    // 获取教练信息
    const coachRes = await db.collection('coaches')
      .where({ openid: openid })
      .get()
    
    if (coachRes.data.length === 0) {
      return {
        success: false,
        error: '教练信息不存在'
      }
    }
    
    const coach = coachRes.data[0]
    const coachId = coach._id
    
    // 检查时间冲突
    const conflictRes = await db.collection('coach_availability')
      .where({
        coachId: coachId,
        date: date,
        startTime: db.command.lt(endTime),
        endTime: db.command.gt(startTime)
      })
      .get()
    
    if (conflictRes.data.length > 0) {
      return {
        success: false,
        error: '该时间段与现有安排冲突'
      }
    }
    
    // 准备要添加的时间段数据
    const datesToAdd = [date]
    
    // 如果选择每周重复，计算接下来几周的日期
    if (repeat === 'weekly') {
      const baseDate = new Date(date)
      for (let i = 1; i <= 8; i++) { // 添加接下来8周
        const nextWeekDate = new Date(baseDate)
        nextWeekDate.setDate(baseDate.getDate() + (i * 7))
        const nextWeekDateStr = nextWeekDate.toISOString().split('T')[0]
        datesToAdd.push(nextWeekDateStr)
      }
    }
    
    // 为每个日期添加时间段
    const addPromises = datesToAdd.map(async (targetDate) => {
      // 再次检查每个日期的时间冲突
      const dateConflictRes = await db.collection('coach_availability')
        .where({
          coachId: coachId,
          date: targetDate,
          startTime: db.command.lt(endTime),
          endTime: db.command.gt(startTime)
        })
        .get()
      
      if (dateConflictRes.data.length === 0) {
        // 没有冲突，添加时间段
        return db.collection('coach_availability').add({
          data: {
            coachId: coachId,
            date: targetDate,
            startTime: startTime,
            endTime: endTime,
            status: 'available', // 可预约
            appointmentId: null,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        })
      }
      return null
    })
    
    const results = await Promise.all(addPromises)
    const successCount = results.filter(result => result !== null).length
    
    return {
      success: true,
      message: `成功添加 ${successCount} 个时间段`,
      addedCount: successCount
    }
  } catch (error) {
    console.error('添加时间段失败：', error)
    return {
      success: false,
      error: error.message
    }
  }
}
