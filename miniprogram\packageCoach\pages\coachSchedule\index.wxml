<view class="schedule-container">
  <view class="header">
    <text class="title">时间管理</text>
  </view>
  
  <view class="calendar-section">
    <view class="calendar-header">
      <button class="nav-btn" bindtap="prevMonth">‹</button>
      <text class="month-title">{{currentYear}}年{{currentMonth}}月</text>
      <button class="nav-btn" bindtap="nextMonth">›</button>
    </view>
    
    <view class="weekdays">
      <text class="weekday">日</text>
      <text class="weekday">一</text>
      <text class="weekday">二</text>
      <text class="weekday">三</text>
      <text class="weekday">四</text>
      <text class="weekday">五</text>
      <text class="weekday">六</text>
    </view>
    
    <view class="calendar-grid">
      <block wx:for="{{calendarDays}}" wx:key="date">
        <view 
          class="calendar-day {{item.isCurrentMonth ? '' : 'other-month'}} {{item.isToday ? 'today' : ''}} {{item.hasSchedule ? 'has-schedule' : ''}}"
          bindtap="selectDate"
          data-date="{{item.date}}"
          data-is-current-month="{{item.isCurrentMonth}}"
        >
          <text class="day-number">{{item.day}}</text>
          <view class="schedule-dots" wx:if="{{item.scheduleCount > 0}}">
            <text class="dot-count">{{item.scheduleCount}}</text>
          </view>
        </view>
      </block>
    </view>
  </view>
  
  <view class="quick-actions">
    <view class="actions-title">快速设置</view>
    <view class="quick-buttons">
      <button class="quick-btn" bindtap="showWeeklyTemplate">设置每周固定时间</button>
      <button class="quick-btn" bindtap="showBatchAdd">批量添加时间</button>
    </view>
  </view>

  <view class="selected-date-section" wx:if="{{selectedDate}}">
    <view class="date-header">
      <text class="selected-date">{{selectedDateText}}</text>
      <button class="add-schedule-btn" bindtap="addTimeSlot">添加时间段</button>
    </view>
    
    <view class="time-slots">
      <view wx:if="{{daySchedules.length === 0}}" class="empty-tip">
        <text class="empty-text">当天暂无可预约时间</text>
      </view>
      <block wx:else>
        <view 
          wx:for="{{daySchedules}}" 
          wx:key="_id" 
          class="time-slot-card"
        >
          <view class="slot-info">
            <text class="slot-time">{{item.startTime}} - {{item.endTime}}</text>
            <text class="slot-status status-{{item.statusClass}}">{{item.statusText}}</text>
          </view>
          <view class="slot-actions">
            <button 
              class="action-btn edit-btn" 
              bindtap="editTimeSlot" 
              data-id="{{item._id}}"
              wx:if="{{item.status === 'available'}}"
            >
              编辑
            </button>
            <button 
              class="action-btn delete-btn" 
              bindtap="deleteTimeSlot" 
              data-id="{{item._id}}"
              wx:if="{{item.status === 'available'}}"
            >
              删除
            </button>
            <button 
              class="action-btn view-btn" 
              bindtap="viewAppointment" 
              data-id="{{item.appointmentId}}"
              wx:if="{{item.status === 'booked'}}"
            >
              查看预约
            </button>
          </view>
        </view>
      </block>
    </view>
  </view>
  
  <view class="legend-section">
    <view class="legend-title">图例说明</view>
    <view class="legend-items">
      <view class="legend-item">
        <view class="legend-dot available"></view>
        <text class="legend-text">可预约</text>
      </view>
      <view class="legend-item">
        <view class="legend-dot booked"></view>
        <text class="legend-text">已预约</text>
      </view>
      <view class="legend-item">
        <view class="legend-dot completed"></view>
        <text class="legend-text">已完成</text>
      </view>
    </view>
  </view>
</view>

<!-- 添加/编辑时间段弹窗 -->
<view class="modal-overlay" wx:if="{{showTimeModal}}" bindtap="closeTimeModal">
  <view class="time-modal" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">{{editingSlot ? '编辑时间段' : '添加时间段'}}</text>
      <button class="close-btn" bindtap="closeTimeModal">×</button>
    </view>
    
    <view class="modal-content">
      <view class="form-item">
        <text class="form-label">开始时间</text>
        <picker mode="time" value="{{timeForm.startTime}}" bindchange="onStartTimeChange">
          <view class="time-picker">{{timeForm.startTime || '请选择开始时间'}}</view>
        </picker>
      </view>
      
      <view class="form-item">
        <text class="form-label">结束时间</text>
        <picker mode="time" value="{{timeForm.endTime}}" bindchange="onEndTimeChange">
          <view class="time-picker">{{timeForm.endTime || '请选择结束时间'}}</view>
        </picker>
      </view>
      
      <view class="form-item">
        <text class="form-label">重复设置</text>
        <view class="repeat-options">
          <label class="repeat-item">
            <radio value="none" checked="{{timeForm.repeat === 'none'}}" bindtap="onRepeatChange" data-value="none" />
            <text>仅当天</text>
          </label>
          <label class="repeat-item">
            <radio value="weekly" checked="{{timeForm.repeat === 'weekly'}}" bindtap="onRepeatChange" data-value="weekly" />
            <text>每周重复</text>
          </label>
        </view>
      </view>
    </view>
    
    <view class="modal-actions">
      <button class="cancel-btn" bindtap="closeTimeModal">取消</button>
      <button class="confirm-btn" bindtap="saveTimeSlot" disabled="{{!canSave}}">
        {{editingSlot ? '保存' : '添加'}}
      </button>
    </view>
  </view>
</view>

<!-- 每周模板设置弹窗 -->
<view class="modal-overlay" wx:if="{{showWeeklyModal}}" bindtap="closeWeeklyModal">
  <view class="weekly-modal" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">设置每周固定时间</text>
      <button class="close-btn" bindtap="closeWeeklyModal">×</button>
    </view>

    <view class="modal-content">
      <view class="weekdays-section">
        <text class="section-label">📅 选择星期</text>
        <view class="weekdays-grid">
          <block wx:for="{{weekdayOptions}}" wx:key="value">
            <view class="weekday-item {{item.checked ? 'checked' : ''}}" bindtap="toggleWeekday" data-value="{{item.value}}">
              <checkbox checked="{{item.checked}}" color="#667eea" />
              <text>{{item.label}}</text>
            </view>
          </block>
        </view>
        <view class="weekdays-tip">
          <text class="tip-text">点击选择需要设置的星期</text>
        </view>
      </view>

      <view class="time-templates">
        <text class="section-label">⏰ 时间模板</text>
        <view class="templates-list">
          <block wx:for="{{weeklyTimeSlots}}" wx:key="index">
            <view class="template-item">
              <view class="time-range">
                <picker mode="time" value="{{item.startTime}}" bindchange="onTemplateStartTimeChange" data-index="{{index}}">
                  <view class="time-picker-small">{{item.startTime || '选择开始时间'}}</view>
                </picker>
                <text class="time-separator">至</text>
                <picker mode="time" value="{{item.endTime}}" bindchange="onTemplateEndTimeChange" data-index="{{index}}">
                  <view class="time-picker-small">{{item.endTime || '选择结束时间'}}</view>
                </picker>
              </view>
              <button class="remove-template-btn" bindtap="removeTimeTemplate" data-index="{{index}}" wx:if="{{weeklyTimeSlots.length > 1}}">删除</button>
            </view>
          </block>
          <button class="add-template-btn" bindtap="addTimeTemplate">
            <text class="add-icon">+</text>
            <text>添加时间段</text>
          </button>
        </view>
        <view class="templates-tip">
          <text class="tip-text">设置每天的可预约时间段，可添加多个</text>
        </view>
      </view>

      <view class="date-range-section">
        <text class="section-label">📆 应用日期范围</text>
        <view class="date-range">
          <picker mode="date" value="{{weeklyStartDate}}" bindchange="onWeeklyStartDateChange">
            <view class="date-picker">{{weeklyStartDate || '选择开始日期'}}</view>
          </picker>
          <text class="date-separator">至</text>
          <picker mode="date" value="{{weeklyEndDate}}" bindchange="onWeeklyEndDateChange">
            <view class="date-picker">{{weeklyEndDate || '选择结束日期'}}</view>
          </picker>
        </view>
        <view class="date-range-tip">
          <text class="tip-text">选择模板应用的日期范围，建议设置接下来4-8周</text>
        </view>
      </view>
    </view>

    <view class="modal-actions">
      <button class="cancel-btn" bindtap="closeWeeklyModal">取消</button>
      <button class="confirm-btn" bindtap="applyWeeklyTemplate" disabled="{{!canApplyWeekly}}">
        应用模板
      </button>
    </view>
  </view>
</view>
