// 评价标签系统配置
const REVIEW_TAGS = {
  // 教学能力类
  teaching: {
    title: '教学能力',
    color: '#667eea',
    tags: [
      { id: 'patient', text: '耐心细致', icon: '🤗' },
      { id: 'professional', text: '专业技术', icon: '💪' },
      { id: 'clear_explanation', text: '讲解清晰', icon: '💡' },
      { id: 'good_demo', text: '动作示范', icon: '🎯' },
      { id: 'step_by_step', text: '循序渐进', icon: '📈' },
      { id: 'personalized', text: '因材施教', icon: '🎨' },
      { id: 'encouraging', text: '鼓励激励', icon: '🌟' },
      { id: 'strict_standard', text: '要求严格', icon: '⚡' }
    ]
  },

  // 课程体验类
  experience: {
    title: '课程体验',
    color: '#34c759',
    tags: [
      { id: 'fun_class', text: '课程有趣', icon: '😄' },
      { id: 'good_atmosphere', text: '氛围轻松', icon: '🎈' },
      { id: 'time_control', text: '时间把控', icon: '⏰' },
      { id: 'safety_first', text: '注重安全', icon: '🛡️' },
      { id: 'good_warm_up', text: '热身充分', icon: '🔥' },
      { id: 'technique_focus', text: '技巧重点', icon: '🎪' },
      { id: 'strength_training', text: '力量训练', icon: '💪' },
      { id: 'mental_guidance', text: '心理指导', icon: '🧠' }
    ]
  },

  // 沟通服务类
  service: {
    title: '沟通服务',
    color: '#ff9500',
    tags: [
      { id: 'responsive', text: '回复及时', icon: '⚡' },
      { id: 'friendly', text: '态度友好', icon: '😊' },
      { id: 'punctual', text: '准时守约', icon: '⏰' },
      { id: 'flexible_schedule', text: '时间灵活', icon: '📅' },
      { id: 'good_communication', text: '沟通顺畅', icon: '💬' },
      { id: 'helpful', text: '乐于助人', icon: '🤝' },
      { id: 'follow_up', text: '课后跟进', icon: '📞' },
      { id: 'problem_solving', text: '解决问题', icon: '🔧' }
    ]
  },

  // 效果成果类
  results: {
    title: '学习效果',
    color: '#ff6b6b',
    tags: [
      { id: 'quick_progress', text: '进步明显', icon: '🚀' },
      { id: 'breakthrough', text: '突破瓶颈', icon: '💥' },
      { id: 'confidence_boost', text: '增强信心', icon: '💪' },
      { id: 'injury_recovery', text: '伤病康复', icon: '🏥' },
      { id: 'technique_improve', text: '技术提升', icon: '📊' },
      { id: 'strength_gain', text: '力量增长', icon: '💪' },
      { id: 'fear_overcome', text: '克服恐惧', icon: '🦁' },
      { id: 'habit_formation', text: '养成习惯', icon: '🔄' }
    ]
  },

  // 环境设施类
  environment: {
    title: '环境设施',
    color: '#5ac8fa',
    tags: [
      { id: 'good_venue', text: '场地优秀', icon: '🏢' },
      { id: 'clean_environment', text: '环境整洁', icon: '✨' },
      { id: 'good_equipment', text: '器材齐全', icon: '🧗‍♂️' },
      { id: 'convenient_location', text: '位置便利', icon: '📍' },
      { id: 'parking_available', text: '停车方便', icon: '🚗' },
      { id: 'good_facilities', text: '设施完善', icon: '🏗️' },
      { id: 'comfortable_space', text: '空间舒适', icon: '🛋️' },
      { id: 'good_ventilation', text: '通风良好', icon: '💨' }
    ]
  },

  // 性价比类
  value: {
    title: '性价比',
    color: '#af52de',
    tags: [
      { id: 'worth_money', text: '物超所值', icon: '💰' },
      { id: 'reasonable_price', text: '价格合理', icon: '💵' },
      { id: 'good_value', text: '性价比高', icon: '⭐' },
      { id: 'extra_service', text: '额外服务', icon: '🎁' },
      { id: 'flexible_payment', text: '付费灵活', icon: '💳' },
      { id: 'no_hidden_cost', text: '无隐性费用', icon: '🔍' },
      { id: 'discount_available', text: '优惠活动', icon: '🏷️' },
      { id: 'package_deal', text: '套餐划算', icon: '📦' }
    ]
  }
}

// 负面评价标签（用于改进建议）
const NEGATIVE_TAGS = {
  improvement: {
    title: '改进建议',
    color: '#8e8e93',
    tags: [
      { id: 'need_more_patience', text: '需要更耐心', icon: '⏳' },
      { id: 'explanation_unclear', text: '讲解不够清晰', icon: '❓' },
      { id: 'too_fast_pace', text: '节奏过快', icon: '⚡' },
      { id: 'too_slow_pace', text: '节奏过慢', icon: '🐌' },
      { id: 'need_more_demo', text: '需要更多示范', icon: '👀' },
      { id: 'time_management', text: '时间管理', icon: '⏰' },
      { id: 'communication_issue', text: '沟通问题', icon: '💬' },
      { id: 'venue_issue', text: '场地问题', icon: '🏢' }
    ]
  }
}

// 获取所有正面标签
function getAllPositiveTags() {
  const allTags = []
  Object.keys(REVIEW_TAGS).forEach(categoryKey => {
    const category = REVIEW_TAGS[categoryKey]
    category.tags.forEach(tag => {
      allTags.push({
        ...tag,
        category: categoryKey,
        categoryTitle: category.title,
        categoryColor: category.color
      })
    })
  })
  return allTags
}

// 获取所有负面标签
function getAllNegativeTags() {
  const allTags = []
  Object.keys(NEGATIVE_TAGS).forEach(categoryKey => {
    const category = NEGATIVE_TAGS[categoryKey]
    category.tags.forEach(tag => {
      allTags.push({
        ...tag,
        category: categoryKey,
        categoryTitle: category.title,
        categoryColor: category.color
      })
    })
  })
  return allTags
}

// 根据标签ID获取标签信息
function getTagById(tagId) {
  const allTags = [...getAllPositiveTags(), ...getAllNegativeTags()]
  return allTags.find(tag => tag.id === tagId)
}

// 根据分类获取标签
function getTagsByCategory(categoryKey) {
  return REVIEW_TAGS[categoryKey] || NEGATIVE_TAGS[categoryKey] || null
}

module.exports = {
  REVIEW_TAGS,
  NEGATIVE_TAGS,
  getAllPositiveTags,
  getAllNegativeTags,
  getTagById,
  getTagsByCategory
}
