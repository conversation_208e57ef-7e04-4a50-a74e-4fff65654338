Page({
  data: {
    currentTab: 'pending',
    appointments: [],
    filteredAppointments: [],
    loading: false,
    errorMsg: '',
    pendingCount: 0,
    tabNames: {
      pending: '待确认',
      confirmed: '已确认',
      completed: '已完成',
      cancelled: '已取消'
    }
  },

  onLoad() {
    this.loadAppointments();
  },

  onShow() {
    // 每次显示页面时刷新数据
    this.loadAppointments();
  },

  async loadAppointments() {
    this.setData({ loading: true, errorMsg: '' });
    
    try {
      const res = await wx.cloud.callFunction({
        name: 'getCoachAppointments',
        data: {}
      });
      
      if (res.result.success) {
        const appointments = this.processAppointments(res.result.data || []);
        this.setData({
          appointments: appointments,
          loading: false
        });
        this.filterAppointments();
        this.updatePendingCount();
      } else {
        this.setData({
          loading: false,
          errorMsg: res.result.error || '加载失败'
        });
      }
    } catch (error) {
      console.error('获取预约列表失败：', error);
      this.setData({
        loading: false,
        errorMsg: '网络错误，请重试'
      });
    }
  },

  processAppointments(appointments) {
    return appointments.map(item => {
      // 处理状态文本
      const statusMap = {
        '待确认': { text: '待确认', class: 'pending' },
        '已确认': { text: '已确认', class: 'confirmed' },
        '已完成': { text: '已完成', class: 'completed' },
        '已取消': { text: '已取消', class: 'cancelled' }
      };
      
      const statusInfo = statusMap[item.status] || { text: item.status, class: 'unknown' };
      
      // 确定可用操作
      const actions = [];
      if (item.status === '待确认') {
        actions.push({ action: 'confirm', text: '确认', type: 'primary' });
        actions.push({ action: 'cancel', text: '取消', type: 'secondary' });
      } else if (item.status === '已确认') {
        actions.push({ action: 'complete', text: '完成', type: 'primary' });
        actions.push({ action: 'cancel', text: '取消', type: 'secondary' });
      }
      
      return {
        ...item,
        statusText: statusInfo.text,
        statusClass: statusInfo.class,
        actions: actions
      };
    });
  },

  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({ currentTab: tab });
    this.filterAppointments();
  },

  filterAppointments() {
    const { appointments, currentTab } = this.data;
    let filtered = [];
    
    switch (currentTab) {
      case 'pending':
        filtered = appointments.filter(item => item.status === '待确认');
        break;
      case 'confirmed':
        filtered = appointments.filter(item => item.status === '已确认');
        break;
      case 'completed':
        filtered = appointments.filter(item => item.status === '已完成');
        break;
      case 'cancelled':
        filtered = appointments.filter(item => item.status === '已取消');
        break;
    }
    
    this.setData({ filteredAppointments: filtered });
  },

  updatePendingCount() {
    const pendingCount = this.data.appointments.filter(item => item.status === '待确认').length;
    this.setData({ pendingCount });
  },

  handleAction(e) {
    const { action, id, studentName } = e.currentTarget.dataset;
    
    switch (action) {
      case 'confirm':
        this.confirmAppointment(id, studentName);
        break;
      case 'cancel':
        this.cancelAppointment(id, studentName);
        break;
      case 'complete':
        this.completeAppointment(id, studentName);
        break;
    }
  },

  confirmAppointment(appointmentId, studentName) {
    wx.showModal({
      title: '确认预约',
      content: `确定要确认${studentName}的预约吗？`,
      success: async (res) => {
        if (res.confirm) {
          await this.updateAppointmentStatus(appointmentId, '已确认');
        }
      }
    });
  },

  cancelAppointment(appointmentId, studentName) {
    wx.showModal({
      title: '取消预约',
      content: `确定要取消${studentName}的预约吗？`,
      success: async (res) => {
        if (res.confirm) {
          await this.updateAppointmentStatus(appointmentId, '已取消');
        }
      }
    });
  },

  completeAppointment(appointmentId, studentName) {
    wx.showModal({
      title: '完成课程',
      content: `确定${studentName}的课程已完成吗？`,
      success: async (res) => {
        if (res.confirm) {
          await this.updateAppointmentStatus(appointmentId, '已完成');
        }
      }
    });
  },

  async updateAppointmentStatus(appointmentId, status) {
    wx.showLoading({ title: '处理中...' });
    
    try {
      const res = await wx.cloud.callFunction({
        name: 'updateAppointmentStatus',
        data: {
          appointmentId,
          status
        }
      });
      
      if (res.result.success) {
        wx.showToast({
          title: '操作成功',
          icon: 'success'
        });
        // 重新加载数据
        this.loadAppointments();
      } else {
        wx.showToast({
          title: res.result.error || '操作失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('更新预约状态失败：', error);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  goToDetail(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/coachAppointmentDetail/index?id=${id}`
    });
  }
});
