const cloud = require('wx-server-sdk')
cloud.init()
const db = cloud.database()

exports.main = async (event, context) => {
  const { courseData } = event
  
  // 获取用户openid
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  if (!courseData) {
    return {
      success: false,
      error: '课程数据不能为空'
    }
  }
  
  try {
    // 获取教练信息
    const coachRes = await db.collection('coaches')
      .where({ openid: openid })
      .get()
    
    if (coachRes.data.length === 0) {
      return {
        success: false,
        error: '教练信息不存在'
      }
    }
    
    const coach = coachRes.data[0]
    const coachId = coach._id
    
    // 验证必填字段
    if (!courseData.name || !courseData.type || !courseData.duration || !courseData.price) {
      console.log('验证失败的字段：', {
        name: courseData.name,
        type: courseData.type,
        duration: courseData.duration,
        price: courseData.price
      });
      return {
        success: false,
        error: '请填写完整的课程信息'
      }
    }

    // 验证数据类型
    const duration = parseInt(courseData.duration);
    const price = parseFloat(courseData.price);

    if (isNaN(duration) || duration <= 0) {
      return {
        success: false,
        error: '课程时长必须是有效的数字（分钟）'
      }
    }

    if (isNaN(price) || price <= 0) {
      return {
        success: false,
        error: '课程价格必须是有效的数字'
      }
    }
    
    // 检查课程名称是否重复
    const existingCourse = await db.collection('coach_courses')
      .where({
        coachId: coachId,
        name: courseData.name
      })
      .get()
    
    if (existingCourse.data.length > 0) {
      return {
        success: false,
        error: '课程名称已存在'
      }
    }
    
    // 创建课程
    const result = await db.collection('coach_courses').add({
      data: {
        coachId: coachId,
        name: courseData.name,
        type: courseData.type,
        duration: duration,
        price: price,
        needDeposit: courseData.needDeposit || false,
        deposit: courseData.deposit || 0,
        description: courseData.description || '',
        outline: courseData.outline || '',
        targetAudience: courseData.targetAudience || '',
        status: courseData.status || 'active',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    })
    
    return {
      success: true,
      message: '课程创建成功',
      courseId: result._id
    }
  } catch (error) {
    console.error('创建课程失败：', error)
    return {
      success: false,
      error: error.message
    }
  }
}
