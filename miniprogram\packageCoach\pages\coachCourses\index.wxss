/* 教练课程管理页样式 */
.courses-container {
  background: #f6f6f6;
  min-height: 100vh;
}

.header {
  background: #667eea;
  padding: 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
}

.add-course-btn {
  background: rgba(255,255,255,0.2);
  color: #fff;
  border: 1rpx solid rgba(255,255,255,0.3);
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
}

.courses-list {
  padding: 16rpx;
}

.loading, .error-msg {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding: 80rpx 0;
}

.error-msg {
  color: #e54545;
}

.empty-tip {
  text-align: center;
  padding: 80rpx 40rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.course-card {
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
}

.course-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.course-info {
  flex: 1;
}

.course-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.course-price {
  font-size: 26rpx;
  color: #667eea;
  font-weight: 600;
}

.course-status {
  margin-left: 16rpx;
}

.status-tag {
  padding: 6rpx 16rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.status-active {
  background: #f6ffed;
  color: #52c41a;
}

.status-inactive {
  background: #fff2f0;
  color: #ff4d4f;
}

.course-content {
  margin-bottom: 16rpx;
}

.info-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8rpx;
}

.label {
  font-size: 24rpx;
  color: #666;
  min-width: 120rpx;
  flex-shrink: 0;
}

.value {
  font-size: 24rpx;
  color: #333;
  flex: 1;
}

.value.description {
  line-height: 1.6;
}

.course-stats {
  display: flex;
  justify-content: space-around;
  background: #f8f9fa;
  border-radius: 8rpx;
  padding: 16rpx;
  margin-bottom: 16rpx;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 28rpx;
  font-weight: 600;
  color: #667eea;
  display: block;
  margin-bottom: 4rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #666;
}

.course-actions {
  display: flex;
  gap: 12rpx;
  justify-content: flex-end;
}

.action-btn {
  border: none;
  border-radius: 16rpx;
  padding: 8rpx 16rpx;
  font-size: 22rpx;
}

.edit-btn {
  background: #667eea;
  color: #fff;
}

.enable-btn {
  background: #52c41a;
  color: #fff;
}

.disable-btn {
  background: #fa8c16;
  color: #fff;
}

.delete-btn {
  background: #ff4757;
  color: #fff;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.course-modal {
  background: #fff;
  border-radius: 12rpx;
  width: 90%;
  max-width: 600rpx;
  max-height: 85vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  margin: 20rpx 0;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  flex-shrink: 0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f0f0f0;
  border: none;
  font-size: 24rpx;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  padding: 32rpx;
  flex: 1;
  overflow-y: auto;
  max-height: calc(85vh - 160rpx);
}

.form-item {
  margin-bottom: 32rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 12rpx;
}

.form-input {
  width: 100%;
  background: #f8f8f8;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  border: 2rpx solid #f8f8f8;
  transition: border-color 0.2s;
}

.form-input:focus {
  border-color: #667eea;
  background: #fff;
}

.form-tip {
  font-size: 22rpx;
  color: #999;
  margin-top: 8rpx;
  display: block;
}

.form-textarea {
  width: 100%;
  background: #f8f8f8;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  border: 2rpx solid #f8f8f8;
  min-height: 120rpx;
  transition: border-color 0.2s;
}

.form-textarea:focus {
  border-color: #667eea;
  background: #fff;
}

.picker-view {
  background: #f8f8f8;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  border: 2rpx solid #f8f8f8;
}

.radio-group {
  display: flex;
  gap: 32rpx;
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 28rpx;
  color: #333;
}

.modal-actions {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
  flex-shrink: 0;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  padding: 24rpx 0;
  border: none;
  font-size: 28rpx;
}

.cancel-btn {
  background: #f8f8f8;
  color: #666;
}

.confirm-btn {
  background: #667eea;
  color: #fff;
}

.confirm-btn[disabled] {
  background: #ccc;
  color: #999;
}
