/* 评价提交页样式 */
.review-container {
  background: #f6f6f6;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.header {
  background: #fff;
  padding: 32rpx;
  text-align: center;
  margin-bottom: 16rpx;
}

.title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.course-info {
  background: #fff;
  padding: 32rpx;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
}

.coach-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 24rpx;
}

.coach-avatar image {
  width: 100%;
  height: 100%;
}

.course-details {
  flex: 1;
}

.coach-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.course-title {
  font-size: 26rpx;
  color: #666;
  display: block;
  margin-bottom: 4rpx;
}

.course-time {
  font-size: 24rpx;
  color: #999;
  display: block;
}

.rating-section, .tags-section, .content-section, .images-section, .anonymous-section {
  background: #fff;
  padding: 32rpx;
  margin-bottom: 16rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 24rpx;
}

.stars {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.star {
  font-size: 48rpx;
  color: #ddd;
  cursor: pointer;
  transition: color 0.2s;
}

.star.active {
  color: #ffb400;
}

.rating-text {
  font-size: 26rpx;
  color: #666;
  margin-left: 16rpx;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.tag-btn {
  background: #f0f0f0;
  border: 2rpx solid #f0f0f0;
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  color: #666;
}

.tag-btn.selected {
  background: #e6f7ff;
  border-color: #007aff;
  color: #007aff;
}

.content-input {
  width: 100%;
  background: #f8f8f8;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 28rpx;
  min-height: 200rpx;
  border: 2rpx solid #f8f8f8;
}

.content-input:focus {
  border-color: #007aff;
  background: #fff;
}

.char-count {
  font-size: 24rpx;
  color: #999;
  text-align: right;
  display: block;
  margin-top: 8rpx;
}

.images-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.image-item {
  width: 160rpx;
  height: 160rpx;
  position: relative;
  border-radius: 8rpx;
  overflow: hidden;
}

.image-item image {
  width: 100%;
  height: 100%;
}

.delete-btn {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 32rpx;
  height: 32rpx;
  background: rgba(0,0,0,0.6);
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
}

.add-image {
  width: 160rpx;
  height: 160rpx;
  border: 2rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
}

.add-icon {
  font-size: 48rpx;
  margin-bottom: 8rpx;
}

.add-text {
  font-size: 22rpx;
}

.image-tip {
  font-size: 24rpx;
  color: #999;
}

.anonymous-checkbox {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #666;
}

.anonymous-checkbox checkbox {
  margin-right: 12rpx;
}

.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 16rpx 32rpx;
  box-shadow: 0 -2rpx 8rpx rgba(0,0,0,0.1);
}

.submit-btn {
  width: 100%;
  background: #007aff;
  color: #fff;
  border-radius: 24rpx;
  padding: 20rpx 0;
  font-size: 32rpx;
  border: none;
  font-weight: 600;
}

.submit-btn[disabled] {
  background: #ccc;
  color: #999;
}