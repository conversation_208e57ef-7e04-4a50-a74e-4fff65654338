const cloud = require('wx-server-sdk')
cloud.init()
const db = cloud.database()

exports.main = async (event, context) => {
  // 获取用户openid
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  try {
    // 获取教练信息
    const coachRes = await db.collection('coaches')
      .where({ openid: openid })
      .get()
    
    if (coachRes.data.length === 0) {
      return {
        success: false,
        error: '教练信息不存在'
      }
    }
    
    const coach = coachRes.data[0]
    const coachId = coach._id
    
    // 获取今日日期
    const today = new Date()
    const todayStr = today.toISOString().split('T')[0]
    
    // 获取本月第一天
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1)
    
    // 查询今日预约
    const todayAppointments = await db.collection('appointments')
      .where({
        coachId: coachId,
        'timeSlot.date': todayStr
      })
      .get()
    
    // 查询本月预约
    const monthlyAppointments = await db.collection('appointments')
      .where({
        coachId: coachId,
        createdAt: db.command.gte(firstDayOfMonth)
      })
      .get()
    
    // 查询待确认预约
    const pendingAppointments = await db.collection('appointments')
      .where({
        coachId: coachId,
        status: '待确认'
      })
      .get()
    
    // 查询最近预约（最近5条）
    const recentAppointments = await db.collection('appointments')
      .where({
        coachId: coachId
      })
      .orderBy('createdAt', 'desc')
      .limit(5)
      .get()
    
    // 查询新评价（最近7天）
    const sevenDaysAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
    const newReviews = await db.collection('reviews')
      .where({
        coachId: coachId,
        createdAt: db.command.gte(sevenDaysAgo)
      })
      .get()
    
    // 计算统计数据
    const todayStats = {
      appointments: todayAppointments.data.length,
      income: todayAppointments.data
        .filter(apt => apt.status === '已完成')
        .reduce((sum, apt) => sum + (apt.price || 0), 0),
      newReviews: newReviews.data.length
    }
    
    const monthlyAppointmentsData = monthlyAppointments.data
    const completedAppointments = monthlyAppointmentsData.filter(apt => apt.status === '已完成')
    
    const monthlyStats = {
      totalAppointments: monthlyAppointmentsData.length,
      totalIncome: completedAppointments.reduce((sum, apt) => sum + (apt.price || 0), 0),
      avgRating: coach.rating || 0,
      completionRate: monthlyAppointmentsData.length > 0 
        ? Math.round((completedAppointments.length / monthlyAppointmentsData.length) * 100)
        : 0
    }
    
    // 处理最近预约数据
    const processedRecentAppointments = await Promise.all(
      recentAppointments.data.map(async (appointment) => {
        return {
          ...appointment,
          studentName: appointment.studentName || '未知学员'
        }
      })
    )
    
    return {
      success: true,
      data: {
        todayStats,
        monthlyStats,
        pendingCount: pendingAppointments.data.length,
        newReviewsCount: newReviews.data.length,
        recentAppointments: processedRecentAppointments
      }
    }
  } catch (error) {
    console.error('获取教练仪表板数据失败：', error)
    return {
      success: false,
      error: error.message
    }
  }
}
