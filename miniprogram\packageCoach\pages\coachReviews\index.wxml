<view class="reviews-container">
  <view class="header">
    <text class="title">评价管理</text>
  </view>
  
  <view class="stats-section">
    <view class="stats-grid">
      <view class="stat-item">
        <text class="stat-number">{{reviewStats.avgRating}}</text>
        <text class="stat-label">平均评分</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{reviewStats.totalReviews}}</text>
        <text class="stat-label">总评价数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{reviewStats.newReviews}}</text>
        <text class="stat-label">本月新增</text>
      </view>
    </view>
  </view>
  
  <view class="reviews-list">
    <view wx:if="{{loading}}" class="loading">加载中...</view>
    <view wx:elif="{{errorMsg}}" class="error-msg">{{errorMsg}}</view>
    <view wx:elif="{{reviews.length === 0}}" class="empty-tip">
      <text class="empty-text">暂无评价</text>
    </view>
    <block wx:else>
      <view 
        wx:for="{{reviews}}" 
        wx:key="_id" 
        class="review-card"
      >
        <view class="review-header">
          <view class="student-info">
            <text class="student-name">{{item.anonymous ? '匿名用户' : item.studentName}}</text>
            <text class="review-time">{{item.createdAtText}}</text>
          </view>
          <view class="rating-stars">
            <block wx:for="{{5}}" wx:key="*this">
              <text class="star {{item.rating > index ? 'active' : ''}}">★</text>
            </block>
          </view>
        </view>
        
        <view class="review-content">
          <text class="content-text">"{{item.content}}"</text>
        </view>
        
        <view class="review-tags" wx:if="{{item.tags && item.tags.length > 0}}">
          <block wx:for="{{item.tags}}" wx:key="*this" wx:for-item="tag">
            <text class="tag">{{tag}}</text>
          </block>
        </view>
        
        <view class="review-course">
          <text class="course-info">课程：{{item.courseTitle}} | {{item.timeSlot.date}}</text>
        </view>
        
        <view class="review-images" wx:if="{{item.images && item.images.length > 0}}">
          <block wx:for="{{item.images}}" wx:key="*this" wx:for-item="image">
            <image class="review-image" src="{{image}}" mode="aspectFill" bindtap="previewImage" data-url="{{image}}" data-urls="{{item.images}}" />
          </block>
        </view>
        
        <view class="review-actions">
          <button class="reply-btn" bindtap="replyToReview" data-id="{{item._id}}" data-student="{{item.studentName}}">
            {{item.reply ? '查看回复' : '回复评价'}}
          </button>
        </view>
        
        <view class="coach-reply" wx:if="{{item.reply}}">
          <view class="reply-header">
            <text class="reply-label">教练回复：</text>
            <text class="reply-time">{{item.replyTime}}</text>
          </view>
          <text class="reply-content">{{item.reply}}</text>
        </view>
      </view>
    </block>
  </view>
</view>
