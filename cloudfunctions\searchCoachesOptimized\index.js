// 优化版教练搜索云函数 - 基于聚合数据结构
const cloud = require('wx-server-sdk')
cloud.init()
const db = cloud.database()

exports.main = async (event, context) => {
  const { 
    keyword = '', 
    city = '', 
    climbingType = '', 
    price = '', 
    specialty = '', 
    sortBy = 'rating', 
    page = 1, 
    pageSize = 20 
  } = event

  try {
    // 构建查询条件 - 基于新的聚合数据结构
    let query = db.collection('coaches').where({
      status: 'active' // 只查询活跃教练
    })

    // 关键词搜索 - 在多个字段中搜索
    if (keyword) {
      query = query.where(db.command.or([
        { 'basicInfo.name': db.command.regex({ regexp: keyword, options: 'i' }) },
        { 'basicInfo.gym': db.command.regex({ regexp: keyword, options: 'i' }) },
        { 'professionalInfo.specialty': db.command.in([keyword]) },
        { 'professionalInfo.intro': db.command.regex({ regexp: keyword, options: 'i' }) }
      ]))
    }

    // 城市筛选
    if (city && city !== '全部') {
      query = query.where({
        'basicInfo.city': city
      })
    }

    // 攀岩类型筛选
    if (climbingType && climbingType !== '全部') {
      query = query.where({
        'professionalInfo.specialty': db.command.in([climbingType])
      })
    }

    // 价格筛选 - 基于课程价格范围
    if (price && price !== '全部') {
      let priceCondition
      if (price === '¥500以下') {
        priceCondition = db.command.lt(500)
      } else if (price === '¥500-800') {
        priceCondition = db.command.gte(500).and(db.command.lte(800))
      } else if (price === '¥800以上') {
        priceCondition = db.command.gt(800)
      }
      
      if (priceCondition) {
        query = query.where({
          'courses.price': priceCondition
        })
      }
    }

    // 专长筛选
    if (specialty && specialty !== '全部') {
      query = query.where({
        'professionalInfo.specialty': db.command.in([specialty])
      })
    }

    // 只返回首页需要的字段，优化性能和数据传输
    const result = await query
      .field({
        // 基本信息
        'basicInfo.name': true,
        'basicInfo.avatar': true,
        'basicInfo.city': true,
        'basicInfo.gym': true,
        // 专业信息
        'professionalInfo.specialty': true,
        'professionalInfo.boulderingLevel': true,
        'professionalInfo.leadLevel': true,
        'professionalInfo.intro': true,
        // 课程信息（只需要第一个课程的价格）
        'courses': true,
        // 联系方式
        'contacts': true,
        // 统计信息
        'stats.rating': true,
        'stats.reviewCount': true,
        // 展示信息
        'display.tags': true,
        'display.highlights': true
      })
      .orderBy(
        sortBy === 'rating' ? 'stats.rating' : 'courses.0.price', 
        sortBy === 'rating' ? 'desc' : 'asc'
      )
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .get()

    // 数据格式化，兼容前端现有逻辑
    const formattedData = result.data.map(coach => {
      // 获取主要课程信息（通常是第一个课程）
      const mainCourse = coach.courses && coach.courses.length > 0 ? coach.courses[0] : {}
      
      return {
        _id: coach._id,
        // 基本信息
        name: coach.basicInfo?.name || '',
        avatar: coach.basicInfo?.avatar || '',
        city: coach.basicInfo?.city || '',
        gym: coach.basicInfo?.gym || '',
        // 专业信息
        specialty: Array.isArray(coach.professionalInfo?.specialty) 
          ? coach.professionalInfo.specialty.join('、') 
          : coach.professionalInfo?.specialty || '',
        boulderingLevel: coach.professionalInfo?.boulderingLevel || '',
        leadLevel: coach.professionalInfo?.leadLevel || '',
        intro: coach.professionalInfo?.intro || '',
        // 课程信息
        price: mainCourse.price || 680,
        courseName: mainCourse.name || '私教课',
        // 统计信息
        rating: coach.stats?.rating || 0,
        reviewCount: coach.stats?.reviewCount || 0,
        // 联系方式
        contacts: coach.contacts || {},
        // 展示信息
        tags: coach.display?.tags || [],
        highlights: coach.display?.highlights || []
      }
    })

    return {
      success: true,
      data: formattedData,
      total: formattedData.length,
      page: page,
      pageSize: pageSize
    }

  } catch (error) {
    console.error('搜索教练失败：', error)
    return {
      success: false,
      error: error.message || '搜索失败，请重试'
    }
  }
}
