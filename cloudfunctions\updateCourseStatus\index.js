const cloud = require('wx-server-sdk')
cloud.init()
const db = cloud.database()

exports.main = async (event, context) => {
  const { courseId, status } = event
  
  // 获取用户openid
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  if (!courseId || !status) {
    return {
      success: false,
      error: '参数不完整'
    }
  }
  
  if (!['active', 'inactive'].includes(status)) {
    return {
      success: false,
      error: '无效的状态值'
    }
  }
  
  try {
    // 获取教练信息
    const coachRes = await db.collection('coaches')
      .where({ openid: openid })
      .get()
    
    if (coachRes.data.length === 0) {
      return {
        success: false,
        error: '教练信息不存在'
      }
    }
    
    const coach = coachRes.data[0]
    const coachId = coach._id
    
    // 获取课程信息并验证权限
    const courseRes = await db.collection('coach_courses').doc(courseId).get()
    if (!courseRes.data) {
      return {
        success: false,
        error: '课程不存在'
      }
    }
    
    if (courseRes.data.coachId !== coachId) {
      return {
        success: false,
        error: '无权限操作此课程'
      }
    }
    
    // 更新课程状态
    await db.collection('coach_courses').doc(courseId).update({
      data: {
        status: status,
        updatedAt: new Date()
      }
    })
    
    return {
      success: true,
      message: `课程已${status === 'active' ? '上架' : '下架'}`
    }
  } catch (error) {
    console.error('更新课程状态失败：', error)
    return {
      success: false,
      error: error.message
    }
  }
}
