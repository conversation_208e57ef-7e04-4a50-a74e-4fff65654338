const cloud = require('wx-server-sdk')
cloud.init()
const db = cloud.database()

exports.main = async (event, context) => {
  // 获取用户openid
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  try {
    // 获取教练信息
    const coachRes = await db.collection('coaches')
      .where({ openid: openid })
      .get()
    
    if (coachRes.data.length === 0) {
      return {
        success: false,
        error: '教练信息不存在'
      }
    }
    
    const coach = coachRes.data[0]
    const coachId = coach._id
    
    // 查询教练的所有课程
    let coursesRes
    try {
      coursesRes = await db.collection('coach_courses')
        .where({
          coachId: coachId
        })
        .orderBy('createdAt', 'desc')
        .get()
    } catch (error) {
      // 如果集合不存在，返回空数组
      if (error.errCode === -502005) {
        console.log('coach_courses 集合不存在，返回空数组')
        return {
          success: true,
          data: []
        }
      }
      throw error
    }
    
    // 为每个课程添加统计信息
    const coursesWithStats = await Promise.all(
      coursesRes.data.map(async (course) => {
        try {
          // 查询该课程的预约统计
          const appointmentsRes = await db.collection('appointments')
            .where({
              coachId: coachId,
              courseId: course._id
            })
            .get()
          
          const appointments = appointmentsRes.data
          const totalBookings = appointments.length
          const completedBookings = appointments.filter(apt => apt.status === '已完成').length
          
          // 查询该课程的评价统计
          const reviewsRes = await db.collection('reviews')
            .where({
              coachId: coachId,
              courseId: course._id
            })
            .get()
          
          const reviews = reviewsRes.data
          const avgRating = reviews.length > 0 
            ? (reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length).toFixed(1)
            : 0
          
          return {
            ...course,
            totalBookings,
            completedBookings,
            avgRating: parseFloat(avgRating)
          }
        } catch (error) {
          console.error(`获取课程 ${course._id} 统计信息失败：`, error)
          return {
            ...course,
            totalBookings: 0,
            completedBookings: 0,
            avgRating: 0
          }
        }
      })
    )
    
    return {
      success: true,
      data: coursesWithStats
    }
  } catch (error) {
    console.error('获取教练课程失败：', error)
    return {
      success: false,
      error: error.message
    }
  }
}
