const cloud = require('wx-server-sdk')
cloud.init()
const db = cloud.database()

exports.main = async (event, context) => {
  const { action, phone, password } = event
  
  // 获取用户openid
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  switch (action) {
    case 'checkCoachStatus':
      return await checkCoachStatus(openid)
    case 'bindCoach':
      return await bindCoach(openid, phone, password)
    case 'getCoachInfo':
      return await getCoachInfo(openid)
    default:
      return {
        success: false,
        error: '未知操作类型'
      }
  }
}

// 检查用户是否为教练
async function checkCoachStatus(openid) {
  try {
    // 查找是否有对应的教练记录
    const coachRes = await db.collection('coaches')
      .where({
        openid: openid
      })
      .get()
    
    if (coachRes.data.length > 0) {
      const coach = coachRes.data[0]
      return {
        success: true,
        data: {
          isCoach: true,
          coachId: coach._id,
          coachInfo: {
            name: coach.name,
            avatar: coach.avatar,
            gym: coach.gym,
            status: coach.status || 'approved'
          },
          // 添加审核状态信息
          approvalStatus: coach.status || 'approved'
        }
      }
    } else {
      return {
        success: true,
        data: {
          isCoach: false
        }
      }
    }
  } catch (error) {
    console.error('检查教练状态失败：', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// 绑定教练身份（密码登录版本）
async function bindCoach(openid, phone, password) {
  try {
    if (!phone || !password) {
      return {
        success: false,
        error: '手机号和密码不能为空'
      }
    }

    // 简单密码检查（测试环境）
    if (password !== '123456') {
      return {
        success: false,
        error: '密码错误'
      }
    }
    
    // 查找是否已有该手机号的教练
    const existingCoach = await db.collection('coaches')
      .where({
        phone: phone
      })
      .get()
    
    if (existingCoach.data.length === 0) {
      return {
        success: false,
        error: '该手机号未注册为教练，请联系管理员'
      }
    }
    
    const coach = existingCoach.data[0]
    
    // 检查是否已绑定其他微信
    if (coach.openid && coach.openid !== openid) {
      return {
        success: false,
        error: '该教练账号已绑定其他微信'
      }
    }
    
    // 绑定openid到教练记录
    await db.collection('coaches').doc(coach._id).update({
      data: {
        openid: openid,
        bindTime: new Date(),
        updatedAt: new Date()
      }
    })
    
    return {
      success: true,
      data: {
        coachId: coach._id,
        message: '教练身份绑定成功'
      }
    }
  } catch (error) {
    console.error('绑定教练身份失败：', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// 获取教练信息
async function getCoachInfo(openid) {
  try {
    const coachRes = await db.collection('coaches')
      .where({
        openid: openid
      })
      .get()
    
    if (coachRes.data.length === 0) {
      return {
        success: false,
        error: '未找到教练信息'
      }
    }
    
    const coach = coachRes.data[0]
    
    return {
      success: true,
      data: {
        coachId: coach._id,
        coach: coach
      }
    }
  } catch (error) {
    console.error('获取教练信息失败：', error)
    return {
      success: false,
      error: error.message
    }
  }
}
