/* 教练个人主页管理样式 */
.profile-container {
  background: #f6f6f6;
  min-height: 100vh;
  padding-bottom: 32rpx;
}

.header {
  background: #667eea;
  padding: 32rpx;
  text-align: center;
}

.title {
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
}

.form-section {
  background: #fff;
  margin: 16rpx;
  border-radius: 12rpx;
  padding: 32rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  padding-bottom: 12rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.form-item {
  margin-bottom: 32rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 12rpx;
}

.form-input {
  width: 100%;
  background: #f8f8f8;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  border: 2rpx solid #f8f8f8;
  transition: border-color 0.2s;
}

.form-input:focus {
  border-color: #667eea;
  background: #fff;
}

.form-textarea {
  width: 100%;
  background: #f8f8f8;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  border: 2rpx solid #f8f8f8;
  min-height: 120rpx;
  transition: border-color 0.2s;
}

.form-textarea:focus {
  border-color: #667eea;
  background: #fff;
}

.form-tip {
  font-size: 22rpx;
  color: #999;
  margin-top: 8rpx;
  display: block;
}

.avatar-section {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.avatar-preview {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid #f0f0f0;
}

.upload-btn {
  background: #667eea;
  color: #fff;
  border: none;
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
}

.radio-group {
  display: flex;
  gap: 32rpx;
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 28rpx;
  color: #333;
}

.price-input {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.currency, .unit {
  font-size: 28rpx;
  color: #666;
}

.price-field {
  flex: 1;
  text-align: center;
}

.gallery-section {
  margin-top: 16rpx;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}

.gallery-item {
  position: relative;
  aspect-ratio: 1;
}

.gallery-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.delete-btn {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background: #ff4757;
  color: #fff;
  border: none;
  font-size: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  line-height: 1;
}

.add-gallery-btn {
  aspect-ratio: 1;
  background: #f8f8f8;
  border: 2rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
}

.add-icon {
  font-size: 48rpx;
  margin-bottom: 8rpx;
}

.add-text {
  font-size: 22rpx;
}

.gallery-tip {
  font-size: 22rpx;
  color: #999;
  text-align: center;
  margin-top: 16rpx;
}

.action-section {
  padding: 0 16rpx;
  margin-top: 32rpx;
}

.save-btn {
  width: 100%;
  background: #667eea;
  color: #fff;
  border: none;
  border-radius: 24rpx;
  padding: 24rpx 0;
  font-size: 32rpx;
  font-weight: 600;
}

.save-btn[disabled] {
  background: #ccc;
  color: #999;
}
