// 数据库迁移云函数 - 将分散的教练数据整合到新的聚合结构
const cloud = require('wx-server-sdk')
cloud.init()
const db = cloud.database()

exports.main = async (event, context) => {
  try {
    console.log('开始数据迁移...')
    
    // 获取所有现有教练数据
    const coachesRes = await db.collection('coaches').get()
    const coaches = coachesRes.data
    
    console.log(`找到 ${coaches.length} 个教练记录`)
    
    const migratedCoaches = []
    
    for (const coach of coaches) {
      try {
        // 获取该教练的课程信息
        const coursesRes = await db.collection('coach_courses')
          .where({ coachId: coach._id })
          .get()
        
        // 获取该教练的时间安排
        const availabilityRes = await db.collection('coach_availability')
          .where({ coachId: coach._id })
          .get()
        
        // 获取该教练的评价统计
        const reviewsRes = await db.collection('reviews')
          .where({ coachId: coach._id })
          .get()
        
        // 计算统计信息
        const reviews = reviewsRes.data
        const totalRating = reviews.reduce((sum, review) => sum + (review.rating || 0), 0)
        const avgRating = reviews.length > 0 ? (totalRating / reviews.length).toFixed(1) : 0
        
        // 构建新的聚合数据结构
        const migratedCoach = {
          _id: coach._id,
          openid: coach.openid,
          
          // 基本信息
          basicInfo: {
            name: coach.name || '',
            avatar: coach.avatar || '',
            gender: coach.gender || '男',
            phone: coach.phone || '',
            city: coach.city || '',
            gym: coach.gym || ''
          },
          
          // 专业信息
          professionalInfo: {
            boulderingLevel: coach.boulderingLevel || '',
            leadLevel: coach.leadLevel || '',
            certifications: coach.certifications || [],
            specialty: Array.isArray(coach.specialty) ? coach.specialty : 
                      (coach.specialty ? coach.specialty.split(',').map(s => s.trim()) : []),
            style: coach.style || '',
            experience: coach.experience || '',
            intro: coach.intro || ''
          },
          
          // 课程信息
          courses: coursesRes.data.map(course => ({
            id: course._id,
            name: course.name || '私教课',
            type: course.type || '室内抱石',
            duration: course.duration || 90,
            price: course.price || coach.price || 680,
            needDeposit: course.needDeposit || coach.needDeposit || false,
            description: course.description || '',
            status: course.status || 'active'
          })),
          
          // 时间安排（简化处理）
          availability: {
            weeklyTemplate: processAvailability(availabilityRes.data),
            specificDates: {}
          },
          
          // 联系方式
          contacts: coach.contacts || {
            wechat: '',
            xiaohongshu: '',
            videoAccount: '',
            contactPhone: coach.phone || ''
          },
          
          // 统计信息
          stats: {
            rating: parseFloat(avgRating),
            reviewCount: reviews.length,
            totalStudents: coach.totalStudents || 0,
            totalCourses: coach.totalCourses || 0
          },
          
          // 展示信息
          display: {
            gallery: coach.gallery || [],
            tags: generateTags(coach),
            highlights: generateHighlights(coach, reviews.length)
          },
          
          // 状态信息
          status: coach.status || 'active',
          createdAt: coach.createdAt || new Date(),
          updatedAt: new Date()
        }
        
        migratedCoaches.push(migratedCoach)
        console.log(`处理教练: ${coach.name}`)
        
      } catch (error) {
        console.error(`处理教练 ${coach.name} 时出错:`, error)
      }
    }
    
    // 批量更新教练数据
    const updatePromises = migratedCoaches.map(coach => 
      db.collection('coaches').doc(coach._id).set({
        data: coach
      })
    )
    
    await Promise.all(updatePromises)
    
    console.log(`数据迁移完成，共处理 ${migratedCoaches.length} 个教练`)
    
    return {
      success: true,
      message: `数据迁移完成，共处理 ${migratedCoaches.length} 个教练`,
      migratedCount: migratedCoaches.length
    }
    
  } catch (error) {
    console.error('数据迁移失败：', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// 辅助函数：处理时间安排数据
function processAvailability(availabilityData) {
  const weeklyTemplate = {
    monday: [],
    tuesday: [],
    wednesday: [],
    thursday: [],
    friday: [],
    saturday: [],
    sunday: []
  }
  
  // 简化处理，将现有时间安排转换为周模板
  availabilityData.forEach(slot => {
    if (slot.dayOfWeek && weeklyTemplate[slot.dayOfWeek]) {
      weeklyTemplate[slot.dayOfWeek].push({
        startTime: slot.startTime || '09:00',
        endTime: slot.endTime || '11:00',
        available: slot.available !== false
      })
    }
  })
  
  return weeklyTemplate
}

// 辅助函数：生成标签
function generateTags(coach) {
  const tags = []
  
  if (coach.boulderingLevel) tags.push(`抱石${coach.boulderingLevel}`)
  if (coach.leadLevel) tags.push(`先锋${coach.leadLevel}`)
  if (coach.style) tags.push(coach.style)
  if (coach.certifications && coach.certifications.length > 0) {
    tags.push('持证教练')
  }
  
  return tags.slice(0, 5) // 最多5个标签
}

// 辅助函数：生成亮点
function generateHighlights(coach, reviewCount) {
  const highlights = []
  
  if (coach.experience) highlights.push(coach.experience)
  if (reviewCount > 10) highlights.push(`${reviewCount}条好评`)
  if (coach.certifications && coach.certifications.length > 0) {
    highlights.push('专业认证')
  }
  
  return highlights.slice(0, 3) // 最多3个亮点
}
