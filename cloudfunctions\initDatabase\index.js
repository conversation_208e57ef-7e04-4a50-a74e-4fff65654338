const cloud = require('wx-server-sdk')
cloud.init()
const db = cloud.database()

exports.main = async (event, context) => {
  try {
    const results = []
    
    // 创建教练课程集合
    try {
      await db.collection('coach_courses').add({
        data: {
          _temp: true,
          createdAt: new Date()
        }
      })
      
      // 立即删除临时数据
      const tempRes = await db.collection('coach_courses').where({ _temp: true }).get()
      if (tempRes.data.length > 0) {
        await db.collection('coach_courses').doc(tempRes.data[0]._id).remove()
      }
      
      results.push('coach_courses 集合创建成功')
    } catch (error) {
      if (error.errCode === -502005) {
        results.push('coach_courses 集合已存在')
      } else {
        results.push(`coach_courses 集合创建失败: ${error.message}`)
      }
    }
    
    // 创建教练时间安排集合
    try {
      await db.collection('coach_availability').add({
        data: {
          _temp: true,
          createdAt: new Date()
        }
      })
      
      // 立即删除临时数据
      const tempRes = await db.collection('coach_availability').where({ _temp: true }).get()
      if (tempRes.data.length > 0) {
        await db.collection('coach_availability').doc(tempRes.data[0]._id).remove()
      }
      
      results.push('coach_availability 集合创建成功')
    } catch (error) {
      if (error.errCode === -502005) {
        results.push('coach_availability 集合已存在')
      } else {
        results.push(`coach_availability 集合创建失败: ${error.message}`)
      }
    }
    
    return {
      success: true,
      message: '数据库初始化完成',
      results: results
    }
  } catch (error) {
    console.error('初始化数据库失败：', error)
    return {
      success: false,
      error: error.message
    }
  }
}
