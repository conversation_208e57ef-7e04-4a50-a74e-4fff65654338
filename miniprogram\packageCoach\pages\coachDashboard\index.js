Page({
  data: {
    coachInfo: {},
    todayStats: {
      appointments: 0,
      income: 0,
      newReviews: 0
    },
    monthlyStats: {
      totalAppointments: 0,
      totalIncome: 0,
      avgRating: 0,
      completionRate: 0
    },
    pendingCount: 0,
    newReviewsCount: 0,
    recentAppointments: [],
    loading: false
  },

  onLoad() {
    this.checkCoachAuth();
  },

  onShow() {
    // 每次显示页面时刷新数据
    if (this.data.coachInfo._id) {
      this.loadDashboardData();
    }
  },

  async checkCoachAuth() {
    try {
      const res = await wx.cloud.callFunction({
        name: 'coachAuth',
        data: {
          action: 'checkCoachStatus'
        }
      });
      
      if (res.result.success && res.result.data.isCoach) {
        this.setData({
          coachInfo: res.result.data.coachInfo
        });
        this.loadDashboardData();
      } else {
        // 不是教练，跳转到登录页
        wx.redirectTo({
          url: '/packageCoach/pages/coachLogin/index'
        });
      }
    } catch (error) {
      console.error('检查教练身份失败：', error);
      wx.showToast({
        title: '身份验证失败',
        icon: 'none'
      });
    }
  },

  async loadDashboardData() {
    this.setData({ loading: true });
    
    try {
      const res = await wx.cloud.callFunction({
        name: 'getCoachDashboard',
        data: {}
      });
      
      if (res.result.success) {
        const data = res.result.data;
        this.setData({
          todayStats: data.todayStats || this.data.todayStats,
          monthlyStats: data.monthlyStats || this.data.monthlyStats,
          pendingCount: data.pendingCount || 0,
          newReviewsCount: data.newReviewsCount || 0,
          recentAppointments: this.processRecentAppointments(data.recentAppointments || []),
          loading: false
        });
      } else {
        console.error('加载数据失败：', res.result.error);
        this.setData({ loading: false });
      }
    } catch (error) {
      console.error('加载仪表板数据失败：', error);
      this.setData({ loading: false });
      // 使用模拟数据
      this.loadMockData();
    }
  },

  loadMockData() {
    // 模拟数据，用于演示
    this.setData({
      todayStats: {
        appointments: 3,
        income: 2040,
        newReviews: 2
      },
      monthlyStats: {
        totalAppointments: 45,
        totalIncome: 30600,
        avgRating: 4.8,
        completionRate: 95
      },
      pendingCount: 2,
      newReviewsCount: 3,
      recentAppointments: [
        {
          _id: '1',
          studentName: '李**',
          timeSlot: {
            date: '2024-01-15',
            start: '14:00',
            end: '15:30'
          },
          status: '待确认',
          statusClass: 'pending',
          statusText: '待确认'
        },
        {
          _id: '2',
          studentName: '王**',
          timeSlot: {
            date: '2024-01-15',
            start: '16:00',
            end: '17:30'
          },
          status: '已确认',
          statusClass: 'confirmed',
          statusText: '已确认'
        }
      ]
    });
  },

  processRecentAppointments(appointments) {
    return appointments.map(item => {
      const statusMap = {
        '待确认': { text: '待确认', class: 'pending' },
        '已确认': { text: '已确认', class: 'confirmed' },
        '已完成': { text: '已完成', class: 'completed' },
        '已取消': { text: '已取消', class: 'cancelled' }
      };
      
      const statusInfo = statusMap[item.status] || { text: item.status, class: 'unknown' };
      
      return {
        ...item,
        statusText: statusInfo.text,
        statusClass: statusInfo.class
      };
    });
  },

  goToAppointments() {
    wx.navigateTo({
      url: '/packageCoach/pages/coachAppointments/index'
    });
  },

  goToSchedule() {
    wx.navigateTo({
      url: '/packageCoach/pages/coachSchedule/index'
    });
  },

  goToReviews() {
    wx.navigateTo({
      url: '/packageCoach/pages/coachReviews/index'
    });
  },

  goToProfile() {
    wx.navigateTo({
      url: '/packageCoach/pages/coachProfile/index'
    });
  },

  goToCourses() {
    wx.navigateTo({
      url: '/packageCoach/pages/coachCourses/index'
    });
  },

  goToEarnings() {
    wx.navigateTo({
      url: '/pages/coachEarnings/index'
    });
  },

  goToAppointmentDetail(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/coachAppointmentDetail/index?id=${id}`
    });
  },

  logout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出教练端吗？',
      success: (res) => {
        if (res.confirm) {
          wx.redirectTo({
            url: '/pages/index/index'
          });
        }
      }
    });
  },

  // 初始化教练端数据库集合
  async initCoachCollections() {
    wx.showLoading({ title: '初始化中...' });

    try {
      const res = await wx.cloud.callFunction({
        name: 'initCoachCollections',
        data: {}
      });

      if (res.result.success) {
        wx.showModal({
          title: '初始化成功',
          content: res.result.results.join('\n'),
          showCancel: false,
          success: () => {
            // 刷新页面数据
            this.loadDashboardData();
          }
        });
      } else {
        wx.showToast({
          title: res.result.error || '初始化失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('初始化教练端集合失败：', error);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  }
});
