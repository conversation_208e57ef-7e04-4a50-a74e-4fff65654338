Page({
  data: {
    courses: [],
    loading: false,
    errorMsg: '',
    showCourseModal: false,
    editingCourse: null,
    courseForm: {
      name: '',
      type: '',
      duration: '',
      price: '',
      needDeposit: false,
      deposit: '',
      description: '',
      outline: '',
      targetAudience: '',
      status: 'active'
    },
    courseTypes: ['私教课', '小班课', '体验课', '进阶课', '竞技课'],
    courseTypeIndex: 0,
    canSaveCourse: false
  },

  onLoad() {
    this.loadCourses();
  },

  onShow() {
    // 每次显示页面时刷新数据
    this.loadCourses();
  },

  async loadCourses() {
    this.setData({ loading: true, errorMsg: '' });
    
    try {
      const res = await wx.cloud.callFunction({
        name: 'getCoachCourses',
        data: {}
      });
      
      if (res.result.success) {
        const courses = this.processCourses(res.result.data || []);
        this.setData({
          courses: courses,
          loading: false
        });
      } else {
        this.setData({
          loading: false,
          errorMsg: res.result.error || '加载失败'
        });
      }
    } catch (error) {
      console.error('获取课程列表失败：', error);
      this.setData({
        loading: false,
        errorMsg: '网络错误，请重试'
      });
    }
  },

  processCourses(courses) {
    return courses.map(item => {
      const statusMap = {
        'active': { text: '已上架', class: 'active' },
        'inactive': { text: '已下架', class: 'inactive' }
      };
      
      const statusInfo = statusMap[item.status] || { text: item.status, class: 'unknown' };
      
      return {
        ...item,
        statusText: statusInfo.text,
        statusClass: statusInfo.class
      };
    });
  },

  addCourse() {
    this.setData({
      showCourseModal: true,
      editingCourse: null,
      courseForm: {
        name: '',
        type: '',
        duration: '',
        price: '',
        needDeposit: false,
        deposit: '',
        description: '',
        outline: '',
        targetAudience: '',
        status: 'active'
      },
      courseTypeIndex: 0,
      canSaveCourse: false
    });
  },

  editCourse(e) {
    const id = e.currentTarget.dataset.id;
    const course = this.data.courses.find(c => c._id === id);
    
    if (course) {
      const typeIndex = this.data.courseTypes.indexOf(course.type);
      
      this.setData({
        showCourseModal: true,
        editingCourse: course,
        courseForm: {
          name: course.name || '',
          type: course.type || '',
          duration: course.duration || '',
          price: course.price || '',
          needDeposit: course.needDeposit || false,
          deposit: course.deposit || '',
          description: course.description || '',
          outline: course.outline || '',
          targetAudience: course.targetAudience || '',
          status: course.status || 'active'
        },
        courseTypeIndex: typeIndex >= 0 ? typeIndex : 0
      });
      this.checkCanSaveCourse();
    }
  },

  closeCourseModal() {
    this.setData({ showCourseModal: false });
  },

  stopPropagation() {
    // 阻止事件冒泡，防止点击弹窗内容时关闭弹窗
  },

  onCourseFormInput(e) {
    const { field } = e.currentTarget.dataset;
    const value = e.detail.value;
    this.setData({
      [`courseForm.${field}`]: value
    });
    this.checkCanSaveCourse();
  },

  onCourseTypeChange(e) {
    const index = e.detail.value;
    this.setData({
      courseTypeIndex: index,
      'courseForm.type': this.data.courseTypes[index]
    });
    this.checkCanSaveCourse();
  },

  onDepositSwitchChange(e) {
    this.setData({
      'courseForm.needDeposit': e.detail.value
    });
    if (!e.detail.value) {
      this.setData({
        'courseForm.deposit': ''
      });
    }
    this.checkCanSaveCourse();
  },

  onStatusChange(e) {
    const value = e.currentTarget.dataset.value;
    this.setData({
      'courseForm.status': value
    });
  },

  checkCanSaveCourse() {
    const { name, type, duration, price, needDeposit, deposit } = this.data.courseForm;
    
    let canSave = name.trim() && type && duration && price;
    
    if (needDeposit) {
      canSave = canSave && deposit;
    }
    
    this.setData({ canSaveCourse: canSave });
  },

  async saveCourse() {
    if (!this.data.canSaveCourse) {
      wx.showToast({
        title: '请填写完整信息',
        icon: 'none'
      });
      return;
    }

    const { courseForm, editingCourse } = this.data;

    // 验证必填字段
    if (!courseForm.name || !courseForm.name.trim()) {
      wx.showToast({
        title: '请输入课程名称',
        icon: 'none'
      });
      return;
    }

    if (!courseForm.type) {
      wx.showToast({
        title: '请选择课程类型',
        icon: 'none'
      });
      return;
    }

    if (!courseForm.duration || courseForm.duration <= 0) {
      wx.showToast({
        title: '请输入有效的课程时长',
        icon: 'none'
      });
      return;
    }

    if (!courseForm.price || courseForm.price <= 0) {
      wx.showToast({
        title: '请输入有效的课程价格',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '保存中...' });

    try {
      const functionName = editingCourse ? 'updateCourse' : 'createCourse';
      console.log('调用云函数：', functionName);
      console.log('课程数据：', courseForm);

      const res = await wx.cloud.callFunction({
        name: functionName,
        data: {
          courseId: editingCourse ? editingCourse._id : undefined,
          courseData: {
            name: courseForm.name.trim(),
            type: courseForm.type,
            duration: parseInt(courseForm.duration),
            price: parseFloat(courseForm.price),
            needDeposit: courseForm.needDeposit,
            deposit: courseForm.needDeposit ? parseFloat(courseForm.deposit || 0) : 0,
            description: courseForm.description ? courseForm.description.trim() : '',
            outline: courseForm.outline ? courseForm.outline.trim() : '',
            targetAudience: courseForm.targetAudience ? courseForm.targetAudience.trim() : '',
            status: courseForm.status || 'active'
          }
        }
      });

      console.log('云函数返回结果：', res);

      if (res.result && res.result.success) {
        wx.hideLoading();
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });

        this.closeCourseModal();
        this.loadCourses();
      } else {
        wx.hideLoading();
        const errorMsg = res.result ? res.result.error : '保存失败';
        console.error('保存失败：', errorMsg);
        wx.showToast({
          title: errorMsg,
          icon: 'none'
        });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('保存课程失败：', error);
      wx.showToast({
        title: `网络错误：${error.message || '请重试'}`,
        icon: 'none'
      });
    }
  },

  toggleCourseStatus(e) {
    const { id, status } = e.currentTarget.dataset;
    const newStatus = status === 'active' ? 'inactive' : 'active';
    const actionText = newStatus === 'active' ? '上架' : '下架';
    
    wx.showModal({
      title: `确认${actionText}`,
      content: `确定要${actionText}这个课程吗？`,
      success: async (res) => {
        if (res.confirm) {
          await this.updateCourseStatus(id, newStatus);
        }
      }
    });
  },

  async updateCourseStatus(courseId, status) {
    wx.showLoading({ title: '更新中...' });
    
    try {
      const res = await wx.cloud.callFunction({
        name: 'updateCourseStatus',
        data: {
          courseId,
          status
        }
      });
      
      if (res.result.success) {
        wx.showToast({
          title: '更新成功',
          icon: 'success'
        });
        this.loadCourses();
      } else {
        wx.showToast({
          title: res.result.error || '更新失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('更新课程状态失败：', error);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  deleteCourse(e) {
    const { id, name } = e.currentTarget.dataset;
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除课程"${name}"吗？此操作不可恢复。`,
      success: async (res) => {
        if (res.confirm) {
          await this.performDeleteCourse(id);
        }
      }
    });
  },

  async performDeleteCourse(courseId) {
    wx.showLoading({ title: '删除中...' });
    
    try {
      const res = await wx.cloud.callFunction({
        name: 'deleteCourse',
        data: { courseId }
      });
      
      if (res.result.success) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });
        this.loadCourses();
      } else {
        wx.showToast({
          title: res.result.error || '删除失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('删除课程失败：', error);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  }
});
