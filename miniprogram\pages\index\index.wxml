<!-- 学员首页 - 发现教练 -->
<view class="container">
  <!-- 固定标题 -->
  <view class="page-title">
    <text class="title-text">发现优秀的攀岩教练</text>
  </view>

  <!-- 主要内容区域 -->
  <view class="main-content">
    <!-- 搜索框 -->
    <view class="search-section">
      <view class="search-box">
        <input class="search-input" placeholder="搜索教练姓名、岩馆或专长" value="{{keyword}}" bindinput="onKeywordChange" />
        <view class="search-icon">🔍</view>
      </view>
    </view>

    <!-- 筛选条件 -->
    <view class="filter-section">
      <view class="filter-row">
        <view class="filter-item">
          <text class="filter-label">城市</text>
          <picker range="{{cityList}}" value="{{selectedCityIndex}}" bindchange="onCityChange">
            <view class="picker-display">
              <text class="picker-text">{{cityList[selectedCityIndex] || '全部'}}</text>
              <text class="picker-arrow">▼</text>
            </view>
          </picker>
        </view>

        <view class="filter-item">
          <text class="filter-label">攀岩类型</text>
          <picker range="{{climbingTypes}}" value="{{selectedClimbingTypeIndex}}" bindchange="onClimbingTypeChange">
            <view class="picker-display">
              <text class="picker-text">{{climbingTypes[selectedClimbingTypeIndex] || '全部'}}</text>
              <text class="picker-arrow">▼</text>
            </view>
          </picker>
        </view>
      </view>

      <view class="filter-row">
        <view class="filter-item">
          <text class="filter-label">价格范围</text>
          <picker range="{{priceRanges}}" value="{{selectedPriceIndex}}" bindchange="onPriceChange">
            <view class="picker-display">
              <text class="picker-text">{{priceRanges[selectedPriceIndex] || '全部'}}</text>
              <text class="picker-arrow">▼</text>
            </view>
          </picker>
        </view>

        <view class="filter-item">
          <text class="filter-label">专长</text>
          <picker range="{{specialties}}" value="{{selectedSpecialtyIndex}}" bindchange="onSpecialtyChange">
            <view class="picker-display">
              <text class="picker-text">{{specialties[selectedSpecialtyIndex] || '全部'}}</text>
              <text class="picker-arrow">▼</text>
            </view>
          </picker>
        </view>
      </view>

      <!-- 重置按钮 -->
      <view class="filter-actions">
        <button class="reset-btn" bindtap="resetFilters">重置筛选</button>
        <text class="result-count">共找到 {{coachList.length}} 位教练</text>
      </view>
    </view>
  </view>

  <!-- 教练结果展示 -->
  <view class="results-section">
    <!-- 加载状态 -->
    <view class="loading-section" wx:if="{{loading}}">
      <view class="loading-spinner"></view>
      <text class="loading-text">搜索中...</text>
    </view>

    <!-- 错误提示 -->
    <view class="error-section" wx:elif="{{errorMsg}}">
      <text class="error-text">{{errorMsg}}</text>
      <button class="retry-btn" bindtap="searchCoaches">重试</button>
    </view>

    <!-- 教练列表 -->
    <view class="coach-results" wx:elif="{{coachList.length > 0}}">
      <block wx:for="{{coachList}}" wx:key="_id">
        <view class="coach-card" bindtap="goToCoachDetail" data-id="{{item._id}}">
          <image class="coach-avatar" src="{{item.avatar}}" mode="aspectFill" binderror="onAvatarError" data-index="{{index}}" />
          <view class="coach-info">
            <view class="coach-header">
              <text class="coach-name">{{item.name}}</text>
              <view class="coach-rating">
                <text class="rating-score">{{item.rating}}</text>
                <text class="rating-star">★</text>
                <text class="rating-count">({{item.reviewCount || 0}})</text>
              </view>
            </view>

            <view class="coach-tags">
              <text class="tag" wx:if="{{item.boulderingLevel}}">抱石{{item.boulderingLevel}}</text>
              <text class="tag" wx:if="{{item.leadLevel}}">先锋{{item.leadLevel}}</text>
              <text class="tag" wx:if="{{item.city}}">{{item.city}}</text>
            </view>

            <text class="coach-specialty">{{item.specialty}}</text>

            <view class="coach-footer">
              <text class="coach-price">¥{{item.price}}/节</text>
              <text class="coach-gym" wx:if="{{item.gym}}">📍 {{item.gym}}</text>
            </view>
          </view>

          <view class="coach-action">
            <text class="action-text">查看详情</text>
          </view>
        </view>
      </block>
    </view>

    <!-- 空状态 -->
    <view class="empty-section" wx:else>
      <view class="empty-icon">🔍</view>
      <text class="empty-text">暂无符合条件的教练</text>
      <text class="empty-desc">试试调整筛选条件</text>
    </view>
  </view>

  <!-- 固定底部导航 -->
  <view class="fixed-bottom-nav">
    <button class="nav-btn appointments-btn" bindtap="goToMyAppointments">
      <view class="nav-icon">📅</view>
      <text class="nav-text">我的记录</text>
    </button>
    <button class="nav-btn coach-btn" bindtap="goToCoachLogin">
      <view class="nav-icon">🧗‍♂️</view>
      <text class="nav-text">教练端</text>
    </button>
    <button class="nav-btn test-btn" bindtap="goToTest">
      <view class="nav-icon">🔧</view>
      <text class="nav-text">测试</text>
    </button>
  </view>
</view>