<view class="courses-container">
  <view class="header">
    <text class="title">课程管理</text>
    <button class="add-course-btn" bindtap="addCourse">添加课程</button>
  </view>
  
  <view class="courses-list">
    <view wx:if="{{loading}}" class="loading">加载中...</view>
    <view wx:elif="{{errorMsg}}" class="error-msg">{{errorMsg}}</view>
    <view wx:elif="{{courses.length === 0}}" class="empty-tip">
      <text class="empty-text">暂无课程，点击右上角添加课程</text>
    </view>
    <block wx:else>
      <view 
        wx:for="{{courses}}" 
        wx:key="_id" 
        class="course-card"
      >
        <view class="course-header">
          <view class="course-info">
            <text class="course-name">{{item.name}}</text>
            <text class="course-price">¥{{item.price}}/课时</text>
          </view>
          <view class="course-status">
            <text class="status-tag status-{{item.statusClass}}">{{item.statusText}}</text>
          </view>
        </view>
        
        <view class="course-content">
          <view class="info-row">
            <text class="label">课程时长：</text>
            <text class="value">{{item.duration}}分钟</text>
          </view>
          <view class="info-row">
            <text class="label">课程类型：</text>
            <text class="value">{{item.type}}</text>
          </view>
          <view class="info-row" wx:if="{{item.needDeposit}}">
            <text class="label">定金：</text>
            <text class="value">¥{{item.deposit}}</text>
          </view>
          <view class="info-row" wx:if="{{item.description}}">
            <text class="label">课程介绍：</text>
            <text class="value description">{{item.description}}</text>
          </view>
        </view>
        
        <view class="course-stats">
          <view class="stat-item">
            <text class="stat-number">{{item.totalBookings || 0}}</text>
            <text class="stat-label">总预约</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{item.completedBookings || 0}}</text>
            <text class="stat-label">已完成</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{item.avgRating || 0}}</text>
            <text class="stat-label">平均评分</text>
          </view>
        </view>
        
        <view class="course-actions">
          <button 
            class="action-btn edit-btn" 
            bindtap="editCourse" 
            data-id="{{item._id}}"
          >
            编辑
          </button>
          <button 
            class="action-btn {{item.status === 'active' ? 'disable-btn' : 'enable-btn'}}" 
            bindtap="toggleCourseStatus" 
            data-id="{{item._id}}"
            data-status="{{item.status}}"
          >
            {{item.status === 'active' ? '下架' : '上架'}}
          </button>
          <button 
            class="action-btn delete-btn" 
            bindtap="deleteCourse" 
            data-id="{{item._id}}"
            data-name="{{item.name}}"
          >
            删除
          </button>
        </view>
      </view>
    </block>
  </view>
</view>

<!-- 添加/编辑课程弹窗 -->
<view class="modal-overlay" wx:if="{{showCourseModal}}" bindtap="closeCourseModal">
  <view class="course-modal" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">{{editingCourse ? '编辑课程' : '添加课程'}}</text>
      <button class="close-btn" bindtap="closeCourseModal">×</button>
    </view>
    
    <scroll-view class="modal-content" scroll-y>
      <view class="form-item">
        <text class="form-label">课程名称</text>
        <input 
          class="form-input"
          placeholder="请输入课程名称"
          value="{{courseForm.name}}"
          bindinput="onCourseFormInput"
          data-field="name"
        />
      </view>
      
      <view class="form-item">
        <text class="form-label">课程类型</text>
        <picker bindchange="onCourseTypeChange" value="{{courseTypeIndex}}" range="{{courseTypes}}">
          <view class="picker-view">{{courseForm.type || '请选择课程类型'}}</view>
        </picker>
      </view>
      
      <view class="form-item">
        <text class="form-label">课程时长（分钟）</text>
        <input
          class="form-input"
          placeholder="请输入数字，如：90"
          type="number"
          value="{{courseForm.duration}}"
          bindinput="onCourseFormInput"
          data-field="duration"
        />
        <text class="form-tip">请输入课程时长的分钟数</text>
      </view>
      
      <view class="form-item">
        <text class="form-label">课程价格（元）</text>
        <input
          class="form-input"
          placeholder="请输入数字，如：680"
          type="number"
          value="{{courseForm.price}}"
          bindinput="onCourseFormInput"
          data-field="price"
        />
        <text class="form-tip">请输入课程价格的数字</text>
      </view>
      
      <view class="form-item">
        <text class="form-label">是否需要定金</text>
        <switch 
          checked="{{courseForm.needDeposit}}" 
          bindchange="onDepositSwitchChange"
          color="#667eea"
        />
      </view>
      
      <view class="form-item" wx:if="{{courseForm.needDeposit}}">
        <text class="form-label">定金金额（元）</text>
        <input 
          class="form-input"
          placeholder="如：100"
          type="number"
          value="{{courseForm.deposit}}"
          bindinput="onCourseFormInput"
          data-field="deposit"
        />
      </view>
      
      <view class="form-item">
        <text class="form-label">课程介绍</text>
        <textarea 
          class="form-textarea"
          placeholder="请介绍课程内容、适合人群等"
          value="{{courseForm.description}}"
          bindinput="onCourseFormInput"
          data-field="description"
          maxlength="500"
        />
      </view>
      
      <view class="form-item">
        <text class="form-label">课程大纲</text>
        <textarea 
          class="form-textarea"
          placeholder="请输入课程大纲，每行一个要点"
          value="{{courseForm.outline}}"
          bindinput="onCourseFormInput"
          data-field="outline"
          maxlength="1000"
        />
      </view>
      
      <view class="form-item">
        <text class="form-label">适合人群</text>
        <input 
          class="form-input"
          placeholder="如：初学者、有一定基础的学员"
          value="{{courseForm.targetAudience}}"
          bindinput="onCourseFormInput"
          data-field="targetAudience"
        />
      </view>
      
      <view class="form-item">
        <text class="form-label">课程状态</text>
        <view class="radio-group">
          <label class="radio-item">
            <radio value="active" checked="{{courseForm.status === 'active'}}" bindtap="onStatusChange" data-value="active" />
            <text>上架</text>
          </label>
          <label class="radio-item">
            <radio value="inactive" checked="{{courseForm.status === 'inactive'}}" bindtap="onStatusChange" data-value="inactive" />
            <text>下架</text>
          </label>
        </view>
      </view>
    </scroll-view>
    
    <view class="modal-actions">
      <button class="cancel-btn" bindtap="closeCourseModal">取消</button>
      <button class="confirm-btn" bindtap="saveCourse" disabled="{{!canSaveCourse}}">
        {{editingCourse ? '保存' : '添加'}}
      </button>
    </view>
  </view>
</view>
