// 云函数：上传默认图片到云存储
const cloud = require('wx-server-sdk')
cloud.init()

exports.main = async (event, context) => {
  try {
    // 创建一个简单的默认头像 (SVG格式，体积小)
    const defaultAvatarSvg = `
      <svg width="120" height="120" viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg">
        <circle cx="60" cy="60" r="60" fill="#f0f0f0"/>
        <circle cx="60" cy="45" r="20" fill="#d0d0d0"/>
        <ellipse cx="60" cy="95" rx="35" ry="25" fill="#d0d0d0"/>
      </svg>
    `
    
    // 创建一个简单的默认封面图 (SVG格式，体积小)
    const defaultCoverSvg = `
      <svg width="300" height="200" viewBox="0 0 300 200" xmlns="http://www.w3.org/2000/svg">
        <rect width="300" height="200" fill="#f5f5f5"/>
        <rect x="50" y="50" width="200" height="100" fill="#e0e0e0" rx="10"/>
        <circle cx="100" cy="75" r="15" fill="#d0d0d0"/>
        <polygon points="130,120 170,80 210,120" fill="#d0d0d0"/>
        <text x="150" y="160" text-anchor="middle" font-family="Arial" font-size="14" fill="#999">攀岩教练</text>
      </svg>
    `
    
    // 上传默认头像
    const avatarUpload = await cloud.uploadFile({
      cloudPath: 'default-avatar.svg',
      fileContent: Buffer.from(defaultAvatarSvg, 'utf8')
    })
    
    // 上传默认封面图
    const coverUpload = await cloud.uploadFile({
      cloudPath: 'default-cover.svg', 
      fileContent: Buffer.from(defaultCoverSvg, 'utf8')
    })
    
    return {
      success: true,
      data: {
        avatarFileId: avatarUpload.fileID,
        coverFileId: coverUpload.fileID
      }
    }
    
  } catch (error) {
    console.error('上传默认图片失败：', error)
    return {
      success: false,
      error: error.message
    }
  }
}
