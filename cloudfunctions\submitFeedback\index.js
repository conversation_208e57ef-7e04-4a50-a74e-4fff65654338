// 提交用户反馈云函数
const cloud = require('wx-server-sdk')
cloud.init()
const db = cloud.database()

exports.main = async (event, context) => {
  const { feedback, type = 'review_tag_suggestion', relatedData = {} } = event
  const { OPENID } = cloud.getWXContext()

  if (!feedback || !feedback.trim()) {
    return {
      success: false,
      error: '反馈内容不能为空'
    }
  }

  try {
    // 保存反馈到数据库
    const result = await db.collection('user_feedback').add({
      data: {
        openid: OPENID,
        type: type, // review_tag_suggestion, general_feedback, bug_report
        content: feedback.trim(),
        relatedData: relatedData, // 相关数据，如评价ID、教练ID等
        status: 'pending', // pending, reviewed, resolved
        createdAt: new Date(),
        updatedAt: new Date()
      }
    })

    console.log('用户反馈提交成功：', result._id)

    return {
      success: true,
      feedbackId: result._id,
      message: '感谢您的反馈，我们会认真考虑您的建议'
    }

  } catch (error) {
    console.error('提交反馈失败：', error)
    return {
      success: false,
      error: '提交失败，请重试'
    }
  }
}
