const cloud = require('wx-server-sdk')
cloud.init()
const db = cloud.database()

exports.main = async (event, context) => {
  const { year, month } = event
  
  // 获取用户openid
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  try {
    // 获取教练信息
    const coachRes = await db.collection('coaches')
      .where({ openid: openid })
      .get()
    
    if (coachRes.data.length === 0) {
      return {
        success: false,
        error: '教练信息不存在'
      }
    }
    
    const coach = coachRes.data[0]
    const coachId = coach._id
    
    // 计算月份的开始和结束日期
    const startDate = `${year}-${String(month).padStart(2, '0')}-01`
    const nextMonth = month === 12 ? 1 : month + 1
    const nextYear = month === 12 ? year + 1 : year
    const endDate = `${nextYear}-${String(nextMonth).padStart(2, '0')}-01`
    
    // 查询该月的所有时间安排
    const scheduleRes = await db.collection('coach_availability')
      .where({
        coachId: coachId,
        date: db.command.gte(startDate).and(db.command.lt(endDate))
      })
      .get()
    
    return {
      success: true,
      data: scheduleRes.data
    }
  } catch (error) {
    console.error('获取教练时间安排失败：', error)
    return {
      success: false,
      error: error.message
    }
  }
}
