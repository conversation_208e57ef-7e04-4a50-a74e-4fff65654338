const cloud = require('wx-server-sdk')
cloud.init()
const db = cloud.database()

exports.main = async (event, context) => {
  // 获取用户openid
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  try {
    // 获取教练信息
    const coachRes = await db.collection('coaches')
      .where({ openid: openid })
      .get()
    
    if (coachRes.data.length === 0) {
      return {
        success: false,
        error: '教练信息不存在'
      }
    }
    
    const coach = coachRes.data[0]
    const coachId = coach._id
    
    // 查询教练的所有评价
    const reviewsRes = await db.collection('reviews')
      .where({
        coachId: coachId
      })
      .orderBy('createdAt', 'desc')
      .get()
    
    const reviews = reviewsRes.data
    
    // 计算统计数据
    const totalReviews = reviews.length
    const avgRating = totalReviews > 0 
      ? (reviews.reduce((sum, review) => sum + review.rating, 0) / totalReviews).toFixed(1)
      : 0
    
    // 计算本月新增评价
    const now = new Date()
    const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
    const newReviews = reviews.filter(review => 
      new Date(review.createdAt) >= firstDayOfMonth
    ).length
    
    const stats = {
      avgRating: parseFloat(avgRating),
      totalReviews,
      newReviews
    }
    
    return {
      success: true,
      data: {
        reviews,
        stats
      }
    }
  } catch (error) {
    console.error('获取教练评价失败：', error)
    return {
      success: false,
      error: error.message
    }
  }
}
