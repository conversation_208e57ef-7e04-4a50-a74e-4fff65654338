// 获取教练完整信息云函数 - 用于详情页
const cloud = require('wx-server-sdk')
cloud.init()
const db = cloud.database()

exports.main = async (event, context) => {
  const { coachId } = event

  if (!coachId) {
    return {
      success: false,
      error: '教练ID不能为空'
    }
  }

  try {
    // 获取教练完整信息 - 基于新的聚合数据结构
    const coachRes = await db.collection('coaches')
      .doc(coachId)
      .get()

    if (!coachRes.data) {
      return {
        success: false,
        error: '教练信息不存在'
      }
    }

    const coach = coachRes.data

    // 获取最近的评价（用于详情页展示）
    const recentReviewsRes = await db.collection('reviews')
      .where({ coachId: coachId })
      .orderBy('createdAt', 'desc')
      .limit(10)
      .get()

    // 格式化返回数据，兼容现有前端逻辑
    const formattedCoach = {
      _id: coach._id,
      
      // 基本信息
      name: coach.basicInfo?.name || '',
      avatar: coach.basicInfo?.avatar || '',
      gender: coach.basicInfo?.gender || '男',
      phone: coach.basicInfo?.phone || '',
      city: coach.basicInfo?.city || '',
      gym: coach.basicInfo?.gym || '',
      
      // 专业信息
      boulderingLevel: coach.professionalInfo?.boulderingLevel || '',
      leadLevel: coach.professionalInfo?.leadLevel || '',
      certifications: coach.professionalInfo?.certifications || [],
      specialty: coach.professionalInfo?.specialty || [],
      style: coach.professionalInfo?.style || '',
      experience: coach.professionalInfo?.experience || '',
      intro: coach.professionalInfo?.intro || '',
      
      // 课程信息
      courses: coach.courses || [],
      courseName: coach.courses?.[0]?.name || '私教课',
      price: coach.courses?.[0]?.price || 680,
      needDeposit: coach.courses?.[0]?.needDeposit || false,
      
      // 时间安排
      availability: coach.availability || {},
      
      // 联系方式
      contacts: coach.contacts || {},
      
      // 统计信息
      rating: coach.stats?.rating || 0,
      reviewCount: coach.stats?.reviewCount || 0,
      totalStudents: coach.stats?.totalStudents || 0,
      totalCourses: coach.stats?.totalCourses || 0,
      
      // 展示信息
      gallery: coach.display?.gallery || [],
      tags: coach.display?.tags || [],
      highlights: coach.display?.highlights || [],
      
      // 状态信息
      status: coach.status || 'active',
      createdAt: coach.createdAt,
      updatedAt: coach.updatedAt
    }

    return {
      success: true,
      data: {
        coach: formattedCoach,
        recentReviews: recentReviewsRes.data || []
      }
    }

  } catch (error) {
    console.error('获取教练详情失败：', error)
    return {
      success: false,
      error: error.message || '获取教练信息失败'
    }
  }
}
