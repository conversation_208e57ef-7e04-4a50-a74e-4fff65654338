<view class="profile-container">
  <view class="header">
    <text class="title">个人主页管理</text>
  </view>
  
  <view class="form-section">
    <view class="section-title">基本信息</view>
    
    <view class="form-item">
      <text class="form-label">头像</text>
      <view class="avatar-section">
        <image class="avatar-preview" src="{{profileData.avatar}}" mode="aspectFill" binderror="onAvatarError" />
        <button class="upload-btn" bindtap="uploadAvatar">更换头像</button>
      </view>
    </view>
    
    <view class="form-item">
      <text class="form-label">姓名</text>
      <input 
        class="form-input"
        placeholder="请输入姓名"
        value="{{profileData.name}}"
        bindinput="onInputChange"
        data-field="name"
      />
    </view>
    
    <view class="form-item">
      <text class="form-label">性别</text>
      <view class="radio-group">
        <label class="radio-item">
          <radio value="男" checked="{{profileData.gender === '男'}}" bindtap="onGenderChange" data-value="男" />
          <text>男</text>
        </label>
        <label class="radio-item">
          <radio value="女" checked="{{profileData.gender === '女'}}" bindtap="onGenderChange" data-value="女" />
          <text>女</text>
        </label>
      </view>
    </view>
    
    <view class="form-item">
      <text class="form-label">所在城市</text>
      <input 
        class="form-input"
        placeholder="请输入所在城市"
        value="{{profileData.city}}"
        bindinput="onInputChange"
        data-field="city"
      />
    </view>
    
    <view class="form-item">
      <text class="form-label">所属岩馆</text>
      <input 
        class="form-input"
        placeholder="请输入所属岩馆"
        value="{{profileData.gym}}"
        bindinput="onInputChange"
        data-field="gym"
      />
    </view>
  </view>
  
  <view class="form-section">
    <view class="section-title">攀岩水平</view>
    
    <view class="form-item">
      <text class="form-label">抱石等级</text>
      <input 
        class="form-input"
        placeholder="如：V6"
        value="{{profileData.boulderingLevel}}"
        bindinput="onInputChange"
        data-field="boulderingLevel"
      />
    </view>
    
    <view class="form-item">
      <text class="form-label">先锋等级</text>
      <input 
        class="form-input"
        placeholder="如：5.12a"
        value="{{profileData.leadLevel}}"
        bindinput="onInputChange"
        data-field="leadLevel"
      />
    </view>
  </view>
  
  <view class="form-section">
    <view class="section-title">专业信息</view>
    
    <view class="form-item">
      <text class="form-label">认证资质</text>
      <input 
        class="form-input"
        placeholder="如：CWA L1, IFSC"
        value="{{certificationsText}}"
        bindinput="onCertificationsChange"
      />
      <text class="form-tip">多个资质请用逗号分隔</text>
    </view>
    
    <view class="form-item">
      <text class="form-label">教学特长</text>
      <input 
        class="form-input"
        placeholder="如：入门教学, 心理辅导"
        value="{{specialtyText}}"
        bindinput="onSpecialtyChange"
      />
      <text class="form-tip">多个特长请用逗号分隔</text>
    </view>
    
    <view class="form-item">
      <text class="form-label">教学风格</text>
      <textarea 
        class="form-textarea"
        placeholder="请描述您的教学风格"
        value="{{profileData.style}}"
        bindinput="onInputChange"
        data-field="style"
        maxlength="200"
      />
    </view>
    
    <view class="form-item">
      <text class="form-label">个人简介</text>
      <textarea 
        class="form-textarea"
        placeholder="请介绍您的攀岩经历和教学经验"
        value="{{profileData.intro}}"
        bindinput="onInputChange"
        data-field="intro"
        maxlength="500"
      />
    </view>
  </view>
  
  <view class="form-section">
    <view class="section-title">课程信息</view>
    
    <view class="form-item">
      <text class="form-label">课程名称</text>
      <input 
        class="form-input"
        placeholder="如：私教课"
        value="{{profileData.courseName}}"
        bindinput="onInputChange"
        data-field="courseName"
      />
    </view>
    
    <view class="form-item">
      <text class="form-label">课程价格</text>
      <view class="price-input">
        <text class="currency">¥</text>
        <input 
          class="form-input price-field"
          placeholder="0"
          type="number"
          value="{{profileData.price}}"
          bindinput="onInputChange"
          data-field="price"
        />
        <text class="unit">/课时</text>
      </view>
    </view>
    
    <view class="form-item">
      <text class="form-label">是否需要定金</text>
      <switch 
        checked="{{profileData.needDeposit}}" 
        bindchange="onDepositChange"
        color="#667eea"
      />
    </view>
  </view>

  <view class="form-section">
    <view class="section-title">联系方式</view>
    <view class="section-desc">学员可通过课前咨询获取您的联系方式</view>

    <view class="form-item">
      <text class="form-label">微信号</text>
      <input
        class="form-input"
        placeholder="请输入您的微信号"
        value="{{profileData.contacts.wechat}}"
        bindinput="onContactChange"
        data-field="wechat"
      />
    </view>

    <view class="form-item">
      <text class="form-label">小红书</text>
      <input
        class="form-input"
        placeholder="请输入小红书用户名或链接"
        value="{{profileData.contacts.xiaohongshu}}"
        bindinput="onContactChange"
        data-field="xiaohongshu"
      />
    </view>

    <view class="form-item">
      <text class="form-label">视频号</text>
      <input
        class="form-input"
        placeholder="请输入视频号名称"
        value="{{profileData.contacts.videoAccount}}"
        bindinput="onContactChange"
        data-field="videoAccount"
      />
    </view>

    <view class="form-item">
      <text class="form-label">联系电话</text>
      <input
        class="form-input"
        placeholder="请输入联系电话"
        value="{{profileData.contacts.contactPhone}}"
        bindinput="onContactChange"
        data-field="contactPhone"
        type="number"
      />
    </view>
  </view>

  <view class="form-section">
    <view class="section-title">作品展示</view>
    
    <view class="gallery-section">
      <view class="gallery-grid">
        <block wx:for="{{profileData.gallery}}" wx:key="*this">
          <view class="gallery-item">
            <image class="gallery-image" src="{{item}}" mode="aspectFill" bindtap="previewImage" data-url="{{item}}" />
            <button class="delete-btn" bindtap="deleteGalleryImage" data-index="{{index}}">×</button>
          </view>
        </block>
        <button class="add-gallery-btn" bindtap="addGalleryImage" wx:if="{{profileData.gallery.length < 9}}">
          <text class="add-icon">+</text>
          <text class="add-text">添加图片</text>
        </button>
      </view>
      <text class="gallery-tip">最多可上传9张图片</text>
    </view>
  </view>
  
  <view class="action-section">
    <button class="save-btn" bindtap="saveProfile" disabled="{{saving}}">
      {{saving ? '保存中...' : '保存修改'}}
    </button>
  </view>
</view>
