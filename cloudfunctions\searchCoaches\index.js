// 云函数入口文件
const cloud = require('wx-server-sdk')
cloud.init()
const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const {
    keyword = '',
    climbingType = '',
    city = '',
    level = '',
    specialty = '',
    price = '',
    sortBy = 'rating',
    page = 1,
    pageSize = 20
  } = event

  let query = {}

  // 关键词搜索（姓名、岩馆、专长）
  if (keyword) {
    query.$or = [
      { name: db.RegExp({ regexp: keyword, options: 'i' }) },
      { gym: db.RegExp({ regexp: keyword, options: 'i' }) },
      { specialty: db.RegExp({ regexp: keyword, options: 'i' }) }
    ]
  }

  // 攀岩类型筛选
  if (climbingType && climbingType !== '不限') {
    query.$or = query.$or || []

    // 根据不同攀岩类型设置搜索关键词
    let searchKeywords = []

    switch (climbingType) {
      case '室内抱石':
        searchKeywords = ['室内抱石', '抱石', 'bouldering', '室内', '岩馆']
        break
      case '室内运动攀':
        searchKeywords = ['室内运动攀', '运动攀', 'sport climbing', '室内', '岩馆', '先锋']
        break
      case '深水抱石':
        searchKeywords = ['深水抱石', 'DWS', 'deep water solo', '深水', '海边']
        break
      case '传统攀':
        searchKeywords = ['传统攀', 'trad', 'traditional', '传统攀岩', '保护点']
        break
      case '结组':
        searchKeywords = ['结组', '多段', '长线', '大岩壁', '结绳']
        break
      case '野外运动攀':
        searchKeywords = ['野外运动攀', '户外运动攀', '野外', '户外', '岩场']
        break
      case '野抱':
        searchKeywords = ['野抱', '野外抱石', '户外抱石', '天然岩石']
        break
      case '摄影':
        searchKeywords = ['摄影', '拍摄', '攀岩摄影', '户外摄影']
        break
      case '航拍':
        searchKeywords = ['航拍', '无人机', '空中摄影', '航空摄影']
        break
      case '康复':
        searchKeywords = ['康复', '理疗', '运动康复', '伤病恢复', '物理治疗']
        break
      case '自助烧烤':
        searchKeywords = ['烧烤', 'BBQ', '野餐', '户外聚餐', '自助']
        break
      default:
        searchKeywords = [climbingType]
    }

    // 在多个字段中搜索关键词
    const typeQueries = []
    searchKeywords.forEach(keyword => {
      typeQueries.push(
        { specialty: db.RegExp({ regexp: keyword, options: 'i' }) },
        { intro: db.RegExp({ regexp: keyword, options: 'i' }) },
        { style: db.RegExp({ regexp: keyword, options: 'i' }) },
        { courseName: db.RegExp({ regexp: keyword, options: 'i' }) }
      )
    })

    query.$or.push(...typeQueries)
  }

  // 城市筛选
  if (city && city !== '不限') {
    query.city = city
  }

  // 水平筛选（这里简化处理，实际可以根据抱石或先锋水平筛选）
  if (level && level !== '不限') {
    // 可以根据需要扩展更复杂的水平匹配逻辑
    query.$or = query.$or || []
    query.$or.push(
      { boulderingLevel: db.RegExp({ regexp: level, options: 'i' }) },
      { leadLevel: db.RegExp({ regexp: level, options: 'i' }) }
    )
  }

  // 专长筛选
  if (specialty && specialty !== '不限') {
    query.specialty = db.RegExp({
      regexp: specialty,
      options: 'i',
    })
  }

  // 价格筛选
  if (price && price !== '不限') {
    switch (price) {
      case '¥500以下':
        query.price = db.command.lt(500)
        break
      case '¥500-800':
        query.price = db.command.gte(500).and(db.command.lte(800))
        break
      case '¥800以上':
        query.price = db.command.gt(800)
        break
    }
  }

  try {
    // 构建查询
    let queryBuilder = db.collection('coaches').where(query)

    // 排序
    if (sortBy === 'rating') {
      queryBuilder = queryBuilder.orderBy('rating', 'desc')
    } else if (sortBy === 'price') {
      queryBuilder = queryBuilder.orderBy('price', 'asc')
    } else if (sortBy === 'reviewCount') {
      queryBuilder = queryBuilder.orderBy('reviewCount', 'desc')
    }

    // 分页
    const skip = (page - 1) * pageSize
    queryBuilder = queryBuilder.skip(skip).limit(pageSize)

    const res = await queryBuilder.get()

    // 获取总数（用于分页）
    const countRes = await db.collection('coaches').where(query).count()

    return {
      success: true,
      data: res.data,
      total: countRes.total,
      page,
      pageSize
    }
  } catch (error) {
    console.error('搜索教练失败：', error)
    return {
      success: false,
      error: error.message,
      data: []
    }
  }
}