const cloud = require('wx-server-sdk')
cloud.init()
const db = cloud.database()

exports.main = async (event, context) => {
  const { coachId } = event

  if (!coachId) {
    return {
      success: false,
      error: '缺少教练ID参数'
    }
  }

  try {
    // 获取教练基本信息
    const coachRes = await db.collection('coaches').doc(coachId).get()

    if (!coachRes.data) {
      return {
        success: false,
        error: '教练不存在'
      }
    }

    const coach = coachRes.data

    // 获取教练的课程信息（如果有courses集合的话）
    // 这里先用教练信息中的课程数据
    const courses = [{
      _id: 'default_course',
      title: coach.courseName || '私教课',
      type: '私教课',
      duration: 90,
      price: coach.price,
      needDeposit: coach.needDeposit,
      depositAmount: coach.needDeposit ? Math.round(coach.price * 0.3) : 0,
      location: coach.gym,
      intro: `适合各水平学员，专业${coach.specialty?.join('、') || '攀岩'}指导`
    }]

    // 获取教练评价（如果有reviews集合的话）
    // 这里先返回模拟数据
    const reviews = []

    return {
      success: true,
      data: {
        coach: {
          ...coach,
          _id: coachId
        },
        courses,
        reviews,
        reviewCount: coach.reviewCount || 0,
        avgRating: coach.rating || 0
      }
    }
  } catch (error) {
    console.error('获取教练详情失败：', error)
    return {
      success: false,
      error: error.message
    }
  }
}