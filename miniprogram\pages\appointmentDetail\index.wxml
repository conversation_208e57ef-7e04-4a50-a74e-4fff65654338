<view class="detail-container">
  <view class="header">
    <text class="title">预约详情</text>
  </view>

  <view wx:if="{{loading}}" class="loading">加载中...</view>
  <view wx:elif="{{errorMsg}}" class="error-msg">{{errorMsg}}</view>
  <view wx:else>
    <!-- 状态卡片 -->
    <view class="status-card">
      <view class="status-header">
        <text class="status-text status-{{appointment.statusClass}}">{{appointment.statusText}}</text>
        <text class="appointment-id">预约编号：{{appointmentId}}</text>
      </view>
      <view class="progress-bar">
        <view class="progress-step {{appointment.step >= 1 ? 'active' : ''}}">
          <text class="step-icon">1</text>
          <text class="step-text">预约提交</text>
        </view>
        <view class="progress-line {{appointment.step >= 2 ? 'active' : ''}}"></view>
        <view class="progress-step {{appointment.step >= 2 ? 'active' : ''}}">
          <text class="step-icon">2</text>
          <text class="step-text">教练确认</text>
        </view>
        <view class="progress-line {{appointment.step >= 3 ? 'active' : ''}}"></view>
        <view class="progress-step {{appointment.step >= 3 ? 'active' : ''}}">
          <text class="step-icon">3</text>
          <text class="step-text">课程完成</text>
        </view>
      </view>
    </view>

    <!-- 课程信息 -->
    <view class="course-card">
      <view class="card-header">
        <text class="card-title">课程信息</text>
      </view>
      <view class="course-info">
        <view class="coach-section">
          <image class="coach-avatar" src="{{coach.avatar}}" mode="aspectFill" />
          <view class="coach-details">
            <text class="coach-name">{{coach.name}}</text>
            <text class="coach-gym">{{coach.gym}}</text>
          </view>
        </view>
        <view class="course-details">
          <view class="detail-row">
            <text class="label">课程：</text>
            <text class="value">{{appointment.courseTitle}}</text>
          </view>
          <view class="detail-row">
            <text class="label">时间：</text>
            <text class="value">{{appointment.timeSlot.date}} {{appointment.timeSlot.start}}-{{appointment.timeSlot.end}}</text>
          </view>
          <view class="detail-row">
            <text class="label">地点：</text>
            <text class="value">{{coach.gym}}</text>
          </view>
          <view class="detail-row">
            <text class="label">价格：</text>
            <text class="value">¥{{appointment.price}}</text>
          </view>
          <view class="detail-row" wx:if="{{appointment.deposit > 0}}">
            <text class="label">定金：</text>
            <text class="value deposit-info">¥{{appointment.deposit}} ({{appointment.depositStatusText}})</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 学员信息 -->
    <view class="student-card">
      <view class="card-header">
        <text class="card-title">学员信息</text>
      </view>
      <view class="student-info">
        <view class="detail-row">
          <text class="label">姓名：</text>
          <text class="value">{{appointment.studentName}}</text>
        </view>
        <view class="detail-row">
          <text class="label">电话：</text>
          <text class="value">{{appointment.studentPhone}}</text>
        </view>
        <view class="detail-row" wx:if="{{appointment.remark}}">
          <text class="label">备注：</text>
          <text class="value">{{appointment.remark}}</text>
        </view>
      </view>
    </view>

    <!-- 时间信息 -->
    <view class="time-card">
      <view class="card-header">
        <text class="card-title">时间记录</text>
      </view>
      <view class="time-info">
        <view class="detail-row">
          <text class="label">预约时间：</text>
          <text class="value">{{appointment.createdAtText}}</text>
        </view>
        <view class="detail-row" wx:if="{{appointment.confirmedAt}}">
          <text class="label">确认时间：</text>
          <text class="value">{{appointment.confirmedAtText}}</text>
        </view>
        <view class="detail-row" wx:if="{{appointment.completedAt}}">
          <text class="label">完成时间：</text>
          <text class="value">{{appointment.completedAtText}}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="actions-card" wx:if="{{appointment.actions.length > 0}}">
      <block wx:for="{{appointment.actions}}" wx:key="*this" wx:for-item="action">
        <button
          class="action-btn {{action.type}}"
          bindtap="handleAction"
          data-action="{{action.action}}"
        >
          {{action.text}}
        </button>
      </block>
    </view>
  </view>
</view>