const cloud = require('wx-server-sdk')
cloud.init()
const db = cloud.database()

exports.main = async (event, context) => {
  const { date } = event
  
  // 获取用户openid
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  if (!date) {
    return {
      success: false,
      error: '日期参数不能为空'
    }
  }
  
  try {
    // 获取教练信息
    const coachRes = await db.collection('coaches')
      .where({ openid: openid })
      .get()
    
    if (coachRes.data.length === 0) {
      return {
        success: false,
        error: '教练信息不存在'
      }
    }
    
    const coach = coachRes.data[0]
    const coachId = coach._id
    
    // 查询指定日期的时间安排
    const scheduleRes = await db.collection('coach_availability')
      .where({
        coachId: coachId,
        date: date
      })
      .orderBy('startTime', 'asc')
      .get()
    
    // 处理时间段数据，添加预约信息
    const processedSchedules = await Promise.all(
      scheduleRes.data.map(async (slot) => {
        let appointmentInfo = null
        
        // 如果时间段已被预约，获取预约信息
        if (slot.status === 'booked' && slot.appointmentId) {
          try {
            const appointmentRes = await db.collection('appointments')
              .doc(slot.appointmentId)
              .get()
            
            if (appointmentRes.data) {
              appointmentInfo = {
                studentName: appointmentRes.data.studentName,
                studentPhone: appointmentRes.data.studentPhone,
                courseTitle: appointmentRes.data.courseTitle
              }
            }
          } catch (err) {
            console.error('获取预约信息失败：', err)
          }
        }
        
        return {
          ...slot,
          appointmentInfo
        }
      })
    )
    
    return {
      success: true,
      data: processedSchedules
    }
  } catch (error) {
    console.error('获取当天时间安排失败：', error)
    return {
      success: false,
      error: error.message
    }
  }
}
