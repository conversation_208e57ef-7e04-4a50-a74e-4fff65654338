<view class="appointment-container">
  <view class="header">
    <text class="title">选择时间 - {{coach.courseName || '私教课'}}</text>
    <view class="coach-info">
      <text>教练：{{coach.name}} | 地点：{{coach.gym}}</text>
    </view>
  </view>

  <view class="time-section">
    <text class="section-title">可用时间</text>
    <view wx:if="{{loading}}" class="loading">加载中...</view>
    <view wx:elif="{{errorMsg}}" class="error-msg">{{errorMsg}}</view>
    <view wx:else class="time-slots">
      <block wx:for="{{groupedSlots}}" wx:key="date">
        <view class="date-group">
          <text class="date-title">{{item.dateDisplay}}</text>
          <view class="slots-row">
            <block wx:for="{{item.slots}}" wx:key="index" wx:for-item="slot">
              <button
                class="time-slot {{slot.isBooked ? 'booked' : ''}} {{selectedSlot && selectedSlot.date === slot.date && selectedSlot.start === slot.start ? 'selected' : ''}}"
                bindtap="selectTimeSlot"
                data-slot="{{slot}}"
                disabled="{{slot.isBooked}}"
              >
                {{slot.start}}-{{slot.end}}
              </button>
            </block>
          </view>
        </view>
      </block>
    </view>
  </view>

  <view class="booking-info" wx:if="{{selectedSlot}}">
    <text class="section-title">课程信息确认</text>
    <view class="info-item">
      <text class="label">课程：</text>
      <text class="value">{{coach.courseName || '私教课'}} · 90分钟</text>
    </view>
    <view class="info-item">
      <text class="label">时间：</text>
      <text class="value">{{selectedSlot.date}} {{selectedSlot.start}}-{{selectedSlot.end}}</text>
    </view>
    <view class="info-item">
      <text class="label">价格：</text>
      <text class="value">¥{{coach.price}} {{coach.needDeposit ? '(需定金¥' + depositAmount + ')' : ''}}</text>
    </view>

    <view class="form-section">
      <view class="form-item">
        <text class="form-label">您的姓名：</text>
        <input class="form-input" placeholder="请输入姓名" bindinput="onNameInput" value="{{studentName}}" />
      </view>
      <view class="form-item">
        <text class="form-label">联系电话：</text>
        <input class="form-input" placeholder="请输入手机号" bindinput="onPhoneInput" value="{{studentPhone}}" type="number" />
      </view>
      <view class="form-item">
        <text class="form-label">备注(选填)：</text>
        <textarea class="form-textarea" placeholder="希望重点练习的内容" bindinput="onRemarkInput" value="{{remark}}" />
      </view>
    </view>
  </view>

  <view class="tips" wx:if="{{selectedSlot}}">
    <text class="tips-title">重要提示：</text>
    <text class="tips-content">1. 点击后将获取教练联系方式，请直接沟通。</text>
    <text class="tips-content">2. 本库仅提供教练信息，不介入课程安排。</text>
  </view>

  <view class="bottom-actions" wx:if="{{selectedSlot}}">
    <button class="submit-btn" bindtap="submitAppointment" disabled="{{!canSubmit}}">
      联系教练
    </button>
  </view>
</view>