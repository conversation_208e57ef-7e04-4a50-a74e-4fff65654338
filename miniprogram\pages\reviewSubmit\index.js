const { REVIEW_TAGS, NEGATIVE_TAGS, getAllPositiveTags, getAllNegativeTags } = require('../../utils/reviewTags.js')

Page({
  data: {
    appointmentId: '',
    appointment: null,
    coach: null,
    rating: 0,
    anonymous: false,
    selectedTags: [], // 选中的标签ID数组
    showNegativeTags: false, // 是否显示改进建议标签
    feedback: '', // 后台留言反馈

    // 标签数据
    reviewTags: REVIEW_TAGS,
    negativeTags: NEGATIVE_TAGS,
    allPositiveTags: [],
    allNegativeTags: [],

    ratingTexts: {
      1: '非常差',
      2: '较差',
      3: '一般',
      4: '不错',
      5: '非常好'
    },
    canSubmit: false,
    loading: false
  },

  onLoad(options) {
    this.setData({
      appointmentId: options.appointmentId || '',
      allPositiveTags: getAllPositiveTags(),
      allNegativeTags: getAllNegativeTags()
    });

    if (this.data.appointmentId) {
      this.loadAppointmentInfo();
    } else {
      wx.showToast({
        title: '缺少预约信息',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  async loadAppointmentInfo() {
    wx.showLoading({ title: '加载中' });

    try {
      const res = await wx.cloud.callFunction({
        name: 'getAppointmentDetail',
        data: {
          appointmentId: this.data.appointmentId
        }
      });

      if (res.result.success) {
        const appointment = res.result.data.appointment;
        const coach = res.result.data.coach;

        this.setData({
          appointment,
          coach: coach || {
            name: '未知教练',
            avatar: '../../images/avatar.png'
          }
        });
      } else {
        wx.showToast({
          title: res.result.error || '加载失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('获取预约详情失败：', error);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  setRating(e) {
    const rating = parseInt(e.currentTarget.dataset.rating);
    this.setData({
      rating,
      canSubmit: rating > 0 && this.data.selectedTags.length > 0
    });
  },

  // 切换标签选择
  toggleTag(e) {
    const tagId = e.currentTarget.dataset.tagId;
    const selectedTags = [...this.data.selectedTags];

    const index = selectedTags.indexOf(tagId);
    if (index === -1) {
      // 最多选择8个标签
      if (selectedTags.length >= 8) {
        wx.showToast({
          title: '最多选择8个标签',
          icon: 'none'
        });
        return;
      }
      selectedTags.push(tagId);
    } else {
      selectedTags.splice(index, 1);
    }

    this.setData({
      selectedTags,
      canSubmit: this.data.rating > 0 && selectedTags.length > 0
    });
  },

  // 切换显示改进建议标签
  toggleNegativeTags() {
    this.setData({
      showNegativeTags: !this.data.showNegativeTags
    });
  },

  // 反馈输入
  onFeedbackInput(e) {
    this.setData({
      feedback: e.detail.value
    });
  },

  onAnonymousChange(e) {
    this.setData({ anonymous: e.detail.value });
  },

  async chooseImage() {
    if (this.data.images.length >= 3) {
      wx.showToast({
        title: '最多上传3张图片',
        icon: 'none'
      });
      return;
    }

    try {
      const res = await wx.chooseMedia({
        count: 3 - this.data.images.length,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        sizeType: ['compressed']
      });

      const tempFiles = res.tempFiles.map(file => file.tempFilePath);
      const images = [...this.data.images, ...tempFiles];

      this.setData({ images });
    } catch (error) {
      console.log('选择图片取消或失败', error);
    }
  },

  deleteImage(e) {
    const index = e.currentTarget.dataset.index;
    const images = [...this.data.images];
    images.splice(index, 1);
    this.setData({ images });
  },

  checkCanSubmit() {
    const { rating, content } = this.data;
    const canSubmit = rating > 0 && content.trim().length >= 5;
    this.setData({ canSubmit });
  },

  async uploadImages() {
    if (this.data.images.length === 0) return [];

    const uploadTasks = this.data.images.map(async (filePath) => {
      const cloudPath = `review_images/${Date.now()}_${Math.random().toString(36).substring(2)}.${filePath.match(/\.(\w+)$/)[1]}`;

      try {
        const res = await wx.cloud.uploadFile({
          cloudPath,
          filePath
        });
        return res.fileID;
      } catch (error) {
        console.error('上传图片失败：', error);
        return null;
      }
    });

    const fileIDs = await Promise.all(uploadTasks);
    return fileIDs.filter(fileID => fileID);
  },

  async submitReview() {
    if (!this.data.canSubmit) return;

    const { appointmentId, rating, anonymous, selectedTags, feedback } = this.data;

    if (!appointmentId) {
      wx.showToast({
        title: '缺少预约信息',
        icon: 'none'
      });
      return;
    }

    this.setData({ loading: true });
    wx.showLoading({ title: '提交中...' });

    try {
      // 提交评价
      const res = await wx.cloud.callFunction({
        name: 'submitReview',
        data: {
          appointmentId,
          rating,
          anonymous,
          tags: selectedTags,
          feedback: feedback.trim() // 后台留言
        }
      });

      if (res.result.success) {
        wx.showToast({
          title: '评价提交成功',
          icon: 'success'
        });

        setTimeout(() => {
          wx.redirectTo({
            url: '/pages/myAppointments/index'
          });
        }, 1500);
      } else {
        wx.showToast({
          title: res.result.error || '提交失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('提交评价失败：', error);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
      wx.hideLoading();
    }
  }
});