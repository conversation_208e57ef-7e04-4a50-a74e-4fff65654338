Page({
  data: {
    phone: '',
    password: '',
    loading: false,
    canLogin: false
  },

  onLoad() {
    // 检查是否已经是教练身份
    this.checkCoachStatus();
  },

  async checkCoachStatus() {
    try {
      const res = await wx.cloud.callFunction({
        name: 'coachAuth',
        data: {
          action: 'checkCoachStatus'
        }
      });
      
      if (res.result.success && res.result.data.isCoach) {
        // 已经是教练，直接跳转到教练后台
        wx.redirectTo({
          url: '/packageCoach/pages/coachDashboard/index'
        });
      }
    } catch (error) {
      console.error('检查教练状态失败：', error);
    }
  },

  onPhoneInput(e) {
    const phone = e.detail.value;
    this.setData({ phone });
    this.checkCanLogin();
  },

  onPasswordInput(e) {
    const password = e.detail.value;
    this.setData({ password });
    this.checkCanLogin();
  },

  checkCanLogin() {
    const { phone, password } = this.data;
    const canLogin = /^1[3-9]\d{9}$/.test(phone) && password.length >= 1;
    this.setData({ canLogin });
  },



  async handleLogin() {
    if (!this.data.canLogin || this.data.loading) return;

    const { phone, password } = this.data;
    
    this.setData({ loading: true });
    
    try {
      const res = await wx.cloud.callFunction({
        name: 'coachAuth',
        data: {
          action: 'bindCoach',
          phone,
          password
        }
      });
      
      if (res.result.success) {
        wx.showToast({
          title: '验证成功',
          icon: 'success'
        });
        
        // 跳转到教练后台
        setTimeout(() => {
          wx.redirectTo({
            url: '/packageCoach/pages/coachDashboard/index'
          });
        }, 1500);
      } else {
        wx.showToast({
          title: res.result.error || '验证失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('登录失败：', error);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  goBack() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  contactAdmin() {
    wx.showModal({
      title: '联系管理员',
      content: '请添加微信：admin123 联系管理员注册教练账号',
      showCancel: false
    });
  }
});
